import React, { useState } from 'react';
import { Plus, Loader2 } from 'lucide-react';
import { useWorkspaces } from '@/hooks/useWorkspaces';
import WorkspaceList from '@/components/WorkspaceList';
import CreateWorkspaceModal from '@/components/CreateWorkspaceModal';
import SettingsPanel from '@/components/SettingsPanel';
import Header from '@/components/Header';
import ErrorBoundary from '@/components/ErrorBoundary';
import GlobalUserTabsControl from '@/components/GlobalUserTabsControl';

/**
 * 主应用组件
 */
const App: React.FC = () => {
  const {
    workspaces,
    activeWorkspace,
    settings,
    loading,
    error,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    switchWorkspace,
    addWebsite,
    removeWebsite,
    updateWebsite,
    reorderWorkspaces,
    reorderWebsites,
    updateSettings,
    reload,
  } = useWorkspaces();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  /**
   * 处理创建工作区
   */
  const handleCreateWorkspace = async (name: string, options?: {
    icon?: string;
    color?: string;
    activate?: boolean;
  }) => {
    try {
      await createWorkspace(name, options);
      setShowCreateModal(false);
    } catch (err) {
      console.error('Failed to create workspace:', err);
    }
  };

  /**
   * 处理工作区切换
   */
  const handleSwitchWorkspace = async (workspaceId: string) => {
    try {
      await switchWorkspace(workspaceId);
    } catch (err) {
      console.error('Failed to switch workspace:', err);
    }
  };

  /**
   * 处理添加当前标签页
   */
  const handleAddCurrentTab = async (workspaceId: string) => {
    try {
      // 获取当前活跃标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tab = tabs[0];
        if (tab.url && !tab.url.startsWith('chrome://')) {
          await addWebsite(workspaceId, tab.url, {
            title: tab.title,
            favicon: tab.favIconUrl || undefined,
          });
        }
      }
    } catch (err) {
      console.error('Failed to add current tab:', err);
    }
  };

  /**
   * 处理添加网站URL
   */
  const handleAddWebsiteUrl = async (workspaceId: string, url: string) => {
    try {
      await addWebsite(workspaceId, url, {
        openInNewTab: true,
      });
    } catch (err) {
      console.error('Failed to add website URL:', err);
    }
  };

  /**
   * 处理更新网站
   */
  const handleUpdateWebsite = async (workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => {
    try {
      await updateWebsite(workspaceId, websiteId, updates);
    } catch (err) {
      console.error('Failed to update website:', err);
    }
  };

  /**
   * 处理切换固定状态
   */
  const handleTogglePin = async (workspaceId: string, websiteId: string, isPinned: boolean) => {
    try {
      await updateWebsite(workspaceId, websiteId, { isPinned });
    } catch (err) {
      console.error('Failed to toggle pin state:', err);
    }
  };

  /**
   * 处理批量固定
   */
  const handleBatchPin = async (workspaceId: string, websiteIds: string[], isPinned: boolean) => {
    try {
      // 批量更新网站的固定状态
      for (const websiteId of websiteIds) {
        await updateWebsite(workspaceId, websiteId, { isPinned });
      }
    } catch (err) {
      console.error('Failed to batch pin websites:', err);
    }
  };

  /**
   * 处理批量删除
   */
  const handleBatchDelete = async (workspaceId: string, websiteIds: string[]) => {
    try {
      // 批量删除网站
      for (const websiteId of websiteIds) {
        await removeWebsite(workspaceId, websiteId);
      }
    } catch (err) {
      console.error('Failed to batch delete websites:', err);
    }
  };



  // 加载状态
  if (loading) {
    return (
      <div className="h-full flex items-center justify-center bg-slate-900">
        <div className="flex flex-col items-center gap-3">
          <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          <p className="text-slate-400 text-sm">加载工作区...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="h-full flex items-center justify-center bg-slate-900 p-4">
        <div className="text-center">
          <div className="text-red-500 text-lg font-semibold mb-2">加载失败</div>
          <p className="text-slate-400 text-sm mb-4">{error}</p>
          <button
            onClick={reload}
            className="btn-primary"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="h-full flex flex-col bg-slate-900 text-white">
        {/* 头部 */}
        <Header
          activeWorkspace={activeWorkspace}
          onSettingsClick={() => setShowSettings(true)}
          onCreateWorkspaceClick={() => setShowCreateModal(true)}
        />

        {/* 主内容区域 - 充满布局 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 全局用户标签页控制 */}
          <GlobalUserTabsControl />

          {/* 工作区列表 - 消除左右间距 */}
          <div className="flex-1 relative">
            {workspaces.length === 0 ? (
              <div className="h-full flex items-center justify-center px-4 py-8">
                <div className="text-center">
                  <div className="text-5xl mb-3">🚀</div>
                  <h3 className="text-base font-semibold text-slate-200 mb-2">
                    欢迎使用 WorkSpace Pro
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    创建您的第一个工作区来开始管理标签页
                  </p>
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="btn-primary text-sm mx-auto"
                  >
                    <Plus className="w-4 h-4" />
                    创建工作区
                  </button>
                </div>
              </div>
            ) : (
              <WorkspaceList
                workspaces={workspaces}
                activeWorkspaceId={activeWorkspace?.id || null}
                onSwitchWorkspace={handleSwitchWorkspace}
                onUpdateWorkspace={updateWorkspace}
                onDeleteWorkspace={deleteWorkspace}
                onAddCurrentTab={handleAddCurrentTab}
                onAddWebsiteUrl={handleAddWebsiteUrl}
                onRemoveWebsite={removeWebsite}
                onUpdateWebsite={handleUpdateWebsite}
                onReorderWorkspaces={reorderWorkspaces}
                onReorderWebsites={reorderWebsites}
                onTogglePin={handleTogglePin}
                onBatchPin={handleBatchPin}
                onBatchDelete={handleBatchDelete}
              />
            )}
          </div>
        </div>

        {/* 创建工作区模态框 */}
        {showCreateModal && (
          <CreateWorkspaceModal
            onClose={() => setShowCreateModal(false)}
            onCreate={handleCreateWorkspace}
          />
        )}

        {/* 设置面板 */}
        {showSettings && (
          <SettingsPanel
            onClose={() => setShowSettings(false)}
          />
        )}
      </div>
    </ErrorBoundary>
  );
};

export default App;
