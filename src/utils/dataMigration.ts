import {
  WorkSpace,
  StorageData,
  OperationResult,
  MigrationOptions
} from '@/types/workspace';
import { StorageManager } from './storage';
import { WorkspaceManager } from './workspace';
import { WorkonaTabManager } from './workonaTabManager';
import { ERROR_CODES, WORKONA_STORAGE_KEYS } from './constants';

/**
 * 数据迁移管理器
 * 实现渐进式数据迁移，确保用户数据安全和功能连续性
 */
export class MigrationManager {
  private static readonly CURRENT_VERSION = '1.0.0';
  private static readonly BACKUP_KEY = 'migrationBackup';

  /**
   * 检测当前数据版本
   */
  static async detectDataVersion(): Promise<OperationResult<string>> {
    try {
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success) {
        // 如果没有版本信息，认为是旧版本数据
        return { success: true, data: '0.0.0' };
      }

      return { success: true, data: versionResult.data! };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to detect data version',
          details: error,
        },
      };
    }
  }

  /**
   * 执行 Workona 格式迁移
   */
  static async migrateToWorkonaFormat(
    options: MigrationOptions = {}
  ): Promise<OperationResult<boolean>> {
    try {
      console.log('🚀 开始 Workona 格式数据迁移...');

      // 1. 检测当前版本
      const versionResult = await this.detectDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }

      const currentVersion = versionResult.data!;
      
      // 如果已经是最新版本，跳过迁移
      if (currentVersion === this.CURRENT_VERSION) {
        console.log('✅ 数据已是最新版本，无需迁移');
        return { success: true, data: false };
      }

      // 2. 备份原始数据（如果需要）
      if (options.backupOriginalData !== false) {
        const backupResult = await this.backupOriginalData();
        if (!backupResult.success) {
          console.error('❌ 数据备份失败，中止迁移');
          return { success: false, error: backupResult.error };
        }
        console.log('💾 原始数据备份完成');
      }

      // 3. 获取现有数据
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }

      const data = allDataResult.data!;
      console.log(`📊 检测到 ${data.workspaces.length} 个工作区需要迁移`);

      // 4. 迁移工作区数据
      const migratedWorkspaces = await this.migrateWorkspaces(data.workspaces);
      if (!migratedWorkspaces.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: migratedWorkspaces.error };
      }

      // 5. 初始化 Workona 数据结构
      const initResult = await this.initializeWorkonaData();
      if (!initResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: initResult.error };
      }

      // 6. 更新数据版本
      const versionUpdateResult = await StorageManager.saveDataVersion(this.CURRENT_VERSION);
      if (!versionUpdateResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: versionUpdateResult.error };
      }

      // 7. 验证迁移结果（如果需要）
      if (options.validateAfterMigration !== false) {
        const validationResult = await this.validateMigration();
        if (!validationResult.success) {
          console.error('❌ 迁移验证失败');
          if (options.rollbackOnError !== false) {
            await this.rollbackMigration();
          }
          return { success: false, error: validationResult.error };
        }
        console.log('✅ 迁移验证通过');
      }

      console.log('🎉 Workona 格式迁移完成！');
      return { success: true, data: true };
    } catch (error) {
      console.error('❌ 迁移过程中发生错误:', error);
      
      if (options.rollbackOnError !== false) {
        await this.rollbackMigration();
      }
      
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Migration failed',
          details: error,
        },
      };
    }
  }

  /**
   * 迁移工作区数据
   */
  private static async migrateWorkspaces(workspaces: WorkSpace[]): Promise<OperationResult<WorkSpace[]>> {
    try {
      const migratedWorkspaces: WorkSpace[] = [];

      for (const workspace of workspaces) {
        // 为现有工作区添加 Workona 字段
        const migratedWorkspace: WorkSpace = {
          ...workspace,
          // 添加 Workona 风格字段（如果不存在）
          type: workspace.type || 'saved',
          pos: workspace.pos || workspace.createdAt,
          state: workspace.state || (workspace.isActive ? 'active' : 'inactive'),
          workonaTabIds: workspace.workonaTabIds || [],
          sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          tabOrder: workspace.tabOrder || []
        };

        migratedWorkspaces.push(migratedWorkspace);
        console.log(`✨ 迁移工作区: ${workspace.name} -> Workona 格式`);
      }

      // 保存迁移后的工作区
      const saveResult = await StorageManager.saveWorkspaces(migratedWorkspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      return { success: true, data: migratedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to migrate workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 初始化 Workona 数据结构
   */
  private static async initializeWorkonaData(): Promise<OperationResult<void>> {
    try {
      // 初始化空的 Workona 数据结构
      await StorageManager.saveTabIdMappings([]);
      await StorageManager.saveLocalOpenWorkspaces({});
      await StorageManager.saveTabGroups({});
      await StorageManager.saveWorkspaceSessions({});

      console.log('🏗️ Workona 数据结构初始化完成');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to initialize Workona data',
          details: error,
        },
      };
    }
  }

  /**
   * 备份原始数据
   */
  private static async backupOriginalData(): Promise<OperationResult<void>> {
    try {
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }

      const backupData = {
        ...allDataResult.data!,
        backupTimestamp: Date.now(),
        backupVersion: await this.detectDataVersion()
      };

      await chrome.storage.local.set({
        [this.BACKUP_KEY]: backupData
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to backup original data',
          details: error,
        },
      };
    }
  }

  /**
   * 回滚迁移
   */
  static async rollbackMigration(): Promise<OperationResult<void>> {
    try {
      console.log('🔄 开始回滚迁移...');

      // 获取备份数据
      const result = await chrome.storage.local.get([this.BACKUP_KEY]);
      const backupData = result[this.BACKUP_KEY];

      if (!backupData) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.STORAGE_ERROR,
            message: 'No backup data found for rollback',
          },
        };
      }

      // 恢复原始数据
      await StorageManager.saveWorkspaces(backupData.workspaces);
      await StorageManager.saveSettings(backupData.settings);
      await StorageManager.setActiveWorkspaceId(backupData.activeWorkspaceId);

      // 清理 Workona 数据
      await chrome.storage.local.remove([
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.DATA_VERSION
      ]);

      console.log('✅ 迁移回滚完成');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to rollback migration',
          details: error,
        },
      };
    }
  }

  /**
   * 验证迁移结果
   */
  private static async validateMigration(): Promise<OperationResult<void>> {
    try {
      // 验证数据版本
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success || versionResult.data !== this.CURRENT_VERSION) {
        throw new Error('Data version validation failed');
      }

      // 验证工作区数据
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        throw new Error('Workspaces validation failed');
      }

      const workspaces = workspacesResult.data!;
      for (const workspace of workspaces) {
        // 验证 Workona 字段存在
        if (!workspace.type || !workspace.pos || !workspace.state) {
          throw new Error(`Workspace ${workspace.name} missing Workona fields`);
        }
      }

      // 验证 Workona 数据结构
      const mappingsResult = await StorageManager.getTabIdMappings();
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      
      if (!mappingsResult.success || !sessionsResult.success) {
        throw new Error('Workona data structures validation failed');
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Migration validation failed',
          details: error,
        },
      };
    }
  }

  /**
   * 清理备份数据
   */
  static async cleanupBackup(): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.remove([this.BACKUP_KEY]);
      console.log('🧹 迁移备份数据已清理');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to cleanup backup',
          details: error,
        },
      };
    }
  }
}
