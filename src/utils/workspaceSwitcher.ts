import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult,
  TabInfo
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { WorkspaceManager } from './workspace';
import { WindowManager } from './windowManager';
import { WorkspaceSessionManager } from './workspaceSessionManager';
import { WorkonaTabManager } from './workonaTabManager';
import { isTabMatchingWebsite } from './urlMatcher';
import { ERROR_CODES } from './constants';

/**
 * 工作区切换管理类
 */
export class WorkspaceSwitcher {
  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(
    workspaceId: string,
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`开始切换到工作区: ${workspaceId}`);

      // 获取目标工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 获取当前活跃工作区
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;

      // 获取设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }

      const settings = settingsResult.data!;

      // 合并选项和设置
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? false, // 默认不自动聚焦到第一个标签页
      };

      // 0. Workona 风格会话切换（新增）
      console.log(`🔄 开始 Workona 会话切换到工作区: ${workspaceId}`);
      const sessionSwitchResult = await WorkspaceSessionManager.switchSession(workspaceId, {
        preserveCurrentSession: true,
        restoreTabOrder: true,
        activateFirstTab: switchOptions.focusFirstTab,
        closeOtherWorkspaceTabs: switchOptions.closeOtherTabs
      });

      if (!sessionSwitchResult.success) {
        console.warn('Workona 会话切换失败，继续使用传统切换方式:', sessionSwitchResult.error);
      } else {
        console.log('✅ Workona 会话切换成功');
      }

      // 1. 处理当前窗口中的标签页
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        // 在移动标签页之前，先检查并创建保护性标签页防止窗口关闭
        await this.ensureWindowHasUserTabsBeforeMove();

        // 如果有当前工作区且不是目标工作区，将其标签页移动到专用窗口
        await this.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        // 如果没有当前工作区，检查是否有其他工作区的标签页需要移动
        await this.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }

      // 2. 从目标工作区的专用窗口移动标签页到主窗口
      await this.moveTabsFromWorkspaceWindow(workspace);

      // 3. 智能检查并打开工作区中缺失的网站
      await this.openWorkspaceWebsites(workspace);

      // 4. 处理用户标签页的隐藏状态（如果之前有隐藏的用户标签页）
      await this.handleUserTabsVisibilityState(workspace);

      // 5. 设置为活跃工作区
      await StorageManager.setActiveWorkspaceId(workspaceId);

      // 6. 更新工作区状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data!;
        workspaces.forEach(w => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }

      // 7. 移除自动聚焦逻辑 - 保持用户当前的标签页焦点状态
      // 不再自动激活工作区的第一个网站标签页，让用户保持当前的浏览状态

      // 8. 发送工作区切换完成事件，通知UI更新状态
      await this.notifyWorkspaceSwitchComplete(workspaceId);

      console.log(`成功切换到工作区: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 移动非目标工作区的标签页到专用窗口
   */
  private static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId: string): Promise<OperationResult<void>> {
    try {
      console.log(`检查并移动非目标工作区的标签页到专用窗口，目标工作区: ${targetWorkspaceId}`);

      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.log('获取工作区列表失败:', workspacesResult.error);
        return { success: true }; // 不阻断流程
      }

      const workspaces = workspacesResult.data!;

      // 获取当前窗口的所有标签页
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log('获取当前窗口标签页失败:', currentTabsResult.error);
        return { success: true };
      }

      const currentTabs = currentTabsResult.data!;
      console.log(`当前窗口共有 ${currentTabs.length} 个标签页`);

      // 检查每个非目标工作区，看是否有真正管理的标签页需要移动
      for (const workspace of workspaces) {
        if (workspace.id === targetWorkspaceId) {
          continue; // 跳过目标工作区
        }

        // 使用智能识别逻辑，只移动工作区真正管理的标签页
        const relatedTabs: TabInfo[] = [];

        for (const tab of currentTabs) {
          // 检查是否匹配工作区的网站配置
          const matchingWebsite = workspace.websites.find(website => {
            if (!tab.url.startsWith(website.url)) return false;
            // URL匹配，进一步检查属性是否匹配
            return tab.isPinned === website.isPinned;
          });

          if (matchingWebsite) {
            relatedTabs.push(tab);
          }
        }

        if (relatedTabs.length > 0) {
          console.log(`发现工作区 "${workspace.name}" 的 ${relatedTabs.length} 个真正管理的标签页需要移动到专用窗口`);

          const tabIds = relatedTabs.map(tab => tab.id);
          const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
            tabIds,
            workspace.id,
            workspace.name
          );

          if (moveResult.success) {
            console.log(`成功移动工作区 "${workspace.name}" 的 ${tabIds.length} 个标签页到专用窗口`);
          } else {
            console.error(`移动工作区 "${workspace.name}" 的标签页失败:`, moveResult.error);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error('移动非目标工作区标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move non-target workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 在移动标签页之前确保窗口有用户标签页，预防性创建保护标签页
   */
  private static async ensureWindowHasUserTabsBeforeMove(): Promise<void> {
    try {
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      console.log('🔍 移动前窗口状态检查:', {
        总标签页: allTabs.length,
        固定标签页: allTabs.filter(tab => tab.pinned).length,
        用户标签页: allTabs.filter(tab => !tab.pinned).length
      });

      // 检查是否只有固定标签页
      const pinnedTabs = allTabs.filter(tab => tab.pinned);
      const userTabs = allTabs.filter(tab => !tab.pinned);

      // 如果只有固定标签页且没有用户标签页，预先创建保护性标签页
      if (pinnedTabs.length > 0 && userTabs.length === 0) {
        console.log('⚠️ 检测到只有固定标签页，预先创建保护性标签页防止窗口关闭');
        await chrome.tabs.create({
          url: 'chrome://newtab/',
          active: false  // 不激活，避免干扰用户体验
        });
        console.log('✅ 已创建保护性标签页');
      } else if (userTabs.length > 0) {
        console.log('ℹ️ 窗口有用户标签页，无需创建保护性标签页');
      } else {
        console.log('ℹ️ 窗口没有固定标签页，无需保护');
      }
    } catch (error) {
      console.warn('移动前窗口状态检查失败:', error);
    }
  }

  /**
   * 确保窗口有用户标签页，如果只有固定标签页则创建新标签页
   */
  private static async ensureWindowHasUserTabs(): Promise<void> {
    try {
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 检查是否只有固定标签页
      const pinnedTabs = allTabs.filter(tab => tab.pinned);
      const userTabs = allTabs.filter(tab => !tab.pinned);

      // 如果只有固定标签页且没有用户标签页，创建新标签页
      if (pinnedTabs.length > 0 && userTabs.length === 0) {
        console.log('🔄 只有固定标签页，创建新标签页防止窗口关闭');
        await chrome.tabs.create({
          url: 'chrome://newtab/',
          active: true
        });
        console.log('✅ 已创建新标签页');
      }
    } catch (error) {
      console.warn('检查窗口标签页状态失败:', error);
    }
  }

  /**
   * 将当前窗口的工作区相关标签页移动到专用窗口
   */
  private static async moveCurrentTabsToWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`将工作区 ${workspace.name} 的标签页移动到专用窗口`);

      // 获取当前窗口中与工作区相关的标签页
      console.log(`🔍 正在查找工作区 "${workspace.name}" 的相关标签页...`);
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        console.log(`❌ 获取工作区相关标签页失败:`, workspaceTabsResult.error);
        return { success: true }; // 不阻断流程
      }

      const workspaceTabs = workspaceTabsResult.data!;
      console.log(`📊 找到 ${workspaceTabs.length} 个工作区相关标签页:`,
        workspaceTabs.map(tab => `${tab.id}:${tab.title}(固定:${tab.isPinned})`));

      if (workspaceTabs.length === 0) {
        console.log(`ℹ️ 工作区 ${workspace.name} 没有相关标签页需要移动`);
        return { success: true };
      }

      // 检查是否所有要移动的标签页都是固定标签页
      const allTabsPinned = workspaceTabs.every(tab => tab.isPinned);
      const currentWindow = await chrome.windows.getCurrent();
      const allCurrentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const remainingUserTabs = allCurrentTabs.filter(tab =>
        !tab.pinned && !workspaceTabs.some(wTab => wTab.id === tab.id)
      );

      console.log('📊 移动前状态分析:', {
        要移动的标签页: workspaceTabs.length,
        全部是固定标签页: allTabsPinned,
        移动后剩余用户标签页: remainingUserTabs.length
      });

      // 如果移动后会导致只剩固定标签页，再次确保有保护性标签页
      if (remainingUserTabs.length === 0 && allTabsPinned) {
        console.log('⚠️ 移动操作会导致只剩固定标签页，确保有保护性标签页');
        await this.ensureWindowHasUserTabsBeforeMove();
      }

      // 移动标签页到专用窗口
      const tabIds = workspaceTabs.map(tab => tab.id);
      console.log(`🚀 准备移动标签页到专用窗口:`, tabIds);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        workspace.name
      );

      if (moveResult.success) {
        console.log(`✅ 成功移动 ${tabIds.length} 个标签页到工作区专用窗口`);

        // 移动完成后再次检查窗口状态
        const finalTabs = await chrome.tabs.query({ windowId: currentWindow.id });
        console.log('📊 移动后窗口状态:', {
          剩余标签页: finalTabs.length,
          固定标签页: finalTabs.filter(tab => tab.pinned).length,
          用户标签页: finalTabs.filter(tab => !tab.pinned).length
        });
      } else {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveResult.error);
      }

      return moveResult;
    } catch (error) {
      console.error(`移动当前标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move current tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区专用窗口移动标签页到当前窗口（优化版）
   */
  private static async moveTabsFromWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`📥 从工作区 ${workspace.name} 的专用窗口移动标签页到主窗口`);

      // 从专用窗口移动标签页
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);

      if (moveResult.success) {
        const movedTabs = moveResult.data!;
        console.log(`✅ 成功从专用窗口移动 ${movedTabs.length} 个标签页到主窗口`);

        // 如果移动了标签页，添加短暂延迟确保标签页完全加载到主窗口
        if (movedTabs.length > 0) {
          console.log('⏳ 等待标签页完全加载到主窗口...');
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      } else {
        console.error(`❌ 从专用窗口移动标签页失败:`, moveResult.error);
      }

      return { success: true }; // 即使失败也不阻断流程
    } catch (error) {
      console.error(`❌ 从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检查并自动打开工作区中缺失的网站（修复版）
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔍 智能检查并打开工作区 ${workspace.name} 中缺失的网站`);

      // 如果工作区没有配置网站，处理空工作区情况
      if (!workspace.websites || workspace.websites.length === 0) {
        console.log(`⚠️ 工作区 "${workspace.name}" 没有配置任何网站`);
        const currentTabsResult = await TabManager.getCurrentWindowTabs();
        if (currentTabsResult.success) {
          const currentTabs = currentTabsResult.data!;
          if (currentTabs.length === 0) {
            console.log('📝 主窗口没有标签页，为空工作区创建默认新标签页');
            const newTabResult = await TabManager.createTab('chrome://newtab/', false, true);
            if (newTabResult.success) {
              console.log('✅ 为空工作区成功创建默认新标签页');
            } else {
              console.error('❌ 为空工作区创建默认新标签页失败:', newTabResult.error);
            }
          } else {
            console.log(`✅ 主窗口已有 ${currentTabs.length} 个标签页，空工作区无需额外操作`);
          }
        }
        return { success: true };
      }

      // 添加延迟确保标签页移动操作完全完成
      console.log('⏳ 等待标签页移动操作完全完成...');
      await new Promise(resolve => setTimeout(resolve, 100));

      // 获取当前窗口的所有标签页
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log('❌ 获取当前窗口标签页失败:', currentTabsResult.error);
        return { success: true }; // 不阻断流程
      }

      const currentTabs = currentTabsResult.data!;
      const currentUrls = currentTabs.map(tab => tab.url);
      console.log(`📋 当前窗口已有 ${currentTabs.length} 个标签页:`, currentUrls);

      // 检查每个工作区网站是否已存在，并确保已存在的标签页是固定的
      const missingWebsites = [];
      const existingTabsToPin = [];

      for (const website of workspace.websites) {
        const existingTab = currentTabs.find(tab => tab.url.startsWith(website.url));

        if (!existingTab) {
          // 标签页不存在，需要创建
          missingWebsites.push(website);
          console.log(`🔍 发现缺失的网站: ${website.title} (${website.url})`);
        } else {
          // 标签页存在，检查是否需要固定
          if (!existingTab.isPinned) {
            existingTabsToPin.push({
              tab: existingTab,
              website: website
            });
            console.log(`📌 发现需要固定的标签页: ${website.title} (${website.url})`);
          } else {
            console.log(`✅ 网站已存在且已固定: ${website.title} (${website.url})`);
          }
        }
      }

      // 首先固定已存在但未固定的标签页
      if (existingTabsToPin.length > 0) {
        console.log(`📌 正在固定 ${existingTabsToPin.length} 个已存在的标签页`);
        for (const { tab, website } of existingTabsToPin) {
          try {
            const pinResult = await TabManager.pinTab(tab.id, true);
            if (pinResult.success) {
              console.log(`✅ 已固定标签页: ${website.title} (${tab.url})`);
            } else {
              console.error(`❌ 固定标签页失败 ${website.title}:`, pinResult.error);
            }
          } catch (error) {
            console.error(`❌ 固定标签页异常 ${website.title}:`, error);
          }
        }
      }

      if (missingWebsites.length === 0) {
        console.log(`✅ 工作区 "${workspace.name}" 的所有网站都已打开${existingTabsToPin.length > 0 ? '并已固定' : ''}，无需创建新标签页`);
        // 确保主窗口至少有一个标签页
        if (currentTabs.length === 0) {
          console.log('⚠️ 主窗口没有标签页，创建默认新标签页');
          await this.ensureMainWindowHasTab();
        }
        return { success: true };
      }

      console.log(`🚀 需要打开 ${missingWebsites.length} 个缺失的网站`);
      let successCount = 0;
      let failCount = 0;

      for (const website of missingWebsites) {
        try {
          console.log(`📝 正在创建标签页: ${website.title} (${website.url})`);
          const newTabResult = await TabManager.createTab(
            website.url,
            true, // 简化：工作区标签页都是固定的
            false // 不立即激活，保持当前标签页活跃
          );
          if (newTabResult.success) {
            console.log(`✅ 成功创建标签页: ${website.title}`);
            successCount++;
          } else {
            console.error(`❌ 创建标签页失败 ${website.title}:`, newTabResult.error);
            failCount++;
          }
        } catch (error) {
          console.error(`❌ 处理网站 ${website.title} 时出错:`, error);
          failCount++;
        }
      }

      console.log(`🎯 工作区 "${workspace.name}" 缺失网站打开完成: 成功 ${successCount} 个，失败 ${failCount} 个`);
      return { success: true };
    } catch (error) {
      console.error('❌ 自动打开缺失网站时出错:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to open workspace websites',
          details: error,
        },
      };
    }
  }

  /**
   * 确保主窗口至少有一个标签页
   */
  private static async ensureMainWindowHasTab(): Promise<OperationResult<void>> {
    try {
      const newTabResult = await TabManager.createTab('chrome://newtab/', false, true);
      if (newTabResult.success) {
        console.log('✅ 成功创建默认新标签页');
      } else {
        console.error('❌ 创建默认新标签页失败:', newTabResult.error);
      }
      return { success: true };
    } catch (error) {
      console.error('❌ 确保主窗口有标签页时出错:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to ensure main window has tab',
          details: error,
        },
      };
    }
  }



  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) {
        return { success: false, error: activeIdResult.error };
      }

      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }

      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        // 如果工作区不存在，清除活跃状态
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }

      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检测当前应该激活的工作区（增强版：支持 Workona ID 和智能匹配）
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }

      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data!;

      // 1. 优先通过 Workona ID 映射查找
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      if (workonaIdResult.success && workonaIdResult.data) {
        const workonaId = workonaIdResult.data;
        const workspaceId = workonaId.split('-')[1]; // 从 t-{workspaceId}-{uuid} 提取工作区ID

        const matchingWorkspace = workspaces.find(w => w.id === workspaceId);
        if (matchingWorkspace) {
          console.log(`🔗 通过 Workona ID 检测到活跃工作区: ${matchingWorkspace.name} (${workonaId})`);
          return { success: true, data: matchingWorkspace };
        }
      }

      // 2. 使用增强的工作区标签页匹配器
      const matchResult = await TabManager.WorkspaceTabContentMatcher.isWorkspaceTab(activeTab);
      if (matchResult.isMatch && matchResult.workspaceId) {
        const matchingWorkspace = workspaces.find(w => w.id === matchResult.workspaceId);
        if (matchingWorkspace) {
          console.log(`🏢 通过智能匹配检测到活跃工作区: ${matchingWorkspace.name} (置信度: ${matchResult.confidence})`);
          return { success: true, data: matchingWorkspace };
        }
      }

      // 3. 回退到传统的 URL 前缀匹配
      const matchingWorkspace = workspaces.find(workspace =>
        workspace.websites.some(website => {
          // 使用简单的 URL 前缀匹配
          return activeTab.url.startsWith(website.url);
        })
      );

      if (matchingWorkspace) {
        console.log(`📍 通过传统 URL 匹配检测到活跃工作区: ${matchingWorkspace.name}`);
      }

      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }

      const activeTab = activeTabResult.data!;

      // 添加到工作区
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId, 
        activeTab.url, 
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned,
        }
      );

      if (addResult.success) {
        return { success: true };
      } else {
        return { success: false, error: addResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add current tab to workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace(): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'No workspaces available',
          },
        };
      }

      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }

      const currentWorkspace = currentResult.data;
      let nextIndex = 0;

      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex(w => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }

      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch to next workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 处理工作区切换时的用户标签页隐藏状态
   */
  private static async handleUserTabsVisibilityState(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 检查工作区 "${workspace.name}" 的用户标签页隐藏状态`);

      // 获取工作区的用户标签页隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);

      if (!stateResult.success) {
        console.log('⚠️ 无法获取用户标签页隐藏状态，跳过处理');
        return { success: true };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      if (isHidden && hiddenTabIds.length > 0) {
        console.log(`🔒 工作区 "${workspace.name}" 有 ${hiddenTabIds.length} 个隐藏的用户标签页，需要保持隐藏状态`);

        // 验证隐藏的标签页是否仍然存在
        const existingTabIds: number[] = [];
        for (const tabId of hiddenTabIds) {
          try {
            await chrome.tabs.get(tabId);
            existingTabIds.push(tabId);
          } catch {
            console.log(`⚠️ 隐藏的标签页 ${tabId} 已不存在`);
          }
        }

        // 如果有标签页已不存在，更新隐藏列表
        if (existingTabIds.length !== hiddenTabIds.length) {
          const removedTabIds = hiddenTabIds.filter(id => !existingTabIds.includes(id));
          await WorkspaceManager.clearHiddenTabIds(workspace.id, removedTabIds);

          if (existingTabIds.length === 0) {
            // 如果所有隐藏的标签页都不存在了，清除隐藏状态
            await WorkspaceManager.setUserTabsHiddenState(workspace.id, false, []);
            console.log(`✅ 工作区 "${workspace.name}" 的所有隐藏标签页都已不存在，清除隐藏状态`);
          } else {
            // 更新隐藏列表
            await WorkspaceManager.setUserTabsHiddenState(workspace.id, true, existingTabIds);
            console.log(`✅ 更新工作区 "${workspace.name}" 的隐藏标签页列表，现有 ${existingTabIds.length} 个`);
          }
        }

        // 确保隐藏的标签页不在主窗口中
        if (existingTabIds.length > 0) {
          const currentWindow = await chrome.windows.getCurrent();
          const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
          const tabsToHide = currentTabs.filter(tab => existingTabIds.includes(tab.id!));

          if (tabsToHide.length > 0) {
            console.log(`📤 发现 ${tabsToHide.length} 个应该隐藏的标签页在主窗口中，移动到专用窗口`);
            const tabIdsToMove = tabsToHide.map(tab => tab.id!);

            const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
              tabIdsToMove,
              workspace.id,
              `${workspace.name} - 隐藏的用户标签页`
            );

            if (moveResult.success) {
              console.log(`✅ 成功移动 ${tabIdsToMove.length} 个标签页到专用窗口`);
            } else {
              console.error('❌ 移动隐藏标签页到专用窗口失败:', moveResult.error);
            }
          }
        }
      } else {
        console.log(`✅ 工作区 "${workspace.name}" 没有隐藏的用户标签页`);
      }

      return { success: true };
    } catch (error) {
      console.error('❌ 处理用户标签页隐藏状态时出错:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to handle user tabs visibility state',
          details: error,
        },
      };
    }
  }

  /**
   * 通知工作区切换完成，触发UI状态更新
   */
  private static async notifyWorkspaceSwitchComplete(workspaceId: string): Promise<void> {
    try {
      // 使用状态同步工具发送工作区切换完成事件
      const { WorkspaceStateSync } = await import('./workspaceStateSync');
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, 'switch');
    } catch (error) {
      console.error('通知工作区切换完成失败:', error);
      // 不抛出错误，避免影响工作区切换流程
    }
  }
}
