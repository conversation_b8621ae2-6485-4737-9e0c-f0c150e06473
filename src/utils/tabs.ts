import {
  TabInfo,
  WorkSpace,
  OperationResult,
  WorkonaTabMatchResult
} from '@/types/workspace';
import { ERROR_CODES } from './constants';
import { WorkonaTabManager } from './workonaTabManager';

/**
 * 标签页管理类
 */
export class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,

      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab(): Promise<OperationResult<TabInfo>> {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active tab found',
          },
        };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get active tab',
          details: error,
        },
      };
    }
  }

  /**
   * 检查URL是否已在当前窗口的标签页中打开（工作区隔离优化版）
   */
  static async findTabByUrl(url: string): Promise<OperationResult<TabInfo | null>> {
    try {
      // 首先尝试在当前窗口中精确匹配
      let tabs = await chrome.tabs.query({ url, currentWindow: true });

      // 如果精确匹配失败，尝试在当前窗口中进行域名匹配
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const currentWindowTabs = await chrome.tabs.query({ currentWindow: true });
          tabs = currentWindowTabs.filter(tab => {
            if (!tab.url) return false;
            try {
              const tabDomain = new URL(tab.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          // URL解析失败，返回空结果
          return { success: true, data: null };
        }
      }

      if (tabs.length === 0) {
        return { success: true, data: null };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to find tab by URL',
          details: error,
        },
      };
    }
  }

  /**
   * 创建新标签页
   */
  static async createTab(url: string, pinned: boolean = false, active: boolean = true): Promise<OperationResult<TabInfo>> {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active,
      });

      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab',
          details: error,
        },
      };
    }
  }



  /**
   * 激活标签页
   */
  static async activateTab(tabId: number): Promise<OperationResult<void>> {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to activate tab',
          details: error,
        },
      };
    }
  }

  // Workona 风格：移除固定标签页功能，完全基于 Workona ID 映射管理

  /**
   * 关闭标签页
   */
  static async closeTab(tabId: number): Promise<OperationResult<void>> {
    try {
      // 先取消固定状态
      try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.pinned) {
          await chrome.tabs.update(tabId, { pinned: false });
        }
      } catch (error) {
        // 如果获取或取消固定失败，继续删除操作
        console.warn('取消标签页固定状态失败:', error);
      }

      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds: number[]): Promise<OperationResult<void>> {
    try {
      // 先取消所有标签页的固定状态
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab.pinned) {
            await chrome.tabs.update(tabId, { pinned: false });
          }
        } catch (error) {
          // 如果某个标签页获取或取消固定失败，继续处理其他标签页
          console.warn(`取消标签页 ${tabId} 固定状态失败:`, error);
        }
      }

      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前窗口的所有标签页
   */
  static async getCurrentWindowTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,

      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get current window tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区真正管理的标签页（Workona 风格：完全基于 ID 映射）
   */
  static async getWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`🔍 查找工作区 "${workspace.name}" 的 Workona 管理标签页`);

      // 阶段1：通过 Workona ID 映射查找标签页
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
      if (!workonaTabIds.success) {
        console.log(`❌ 获取工作区 Workona 标签页ID失败:`, workonaTabIds.error);
        return { success: true, data: [] }; // 返回空数组而不是错误
      }

      const relatedTabs: TabInfo[] = [];
      const validWorkonaIds: string[] = [];

      // 将 Workona ID 转换为实际标签页
      for (const workonaId of workonaTabIds.data!) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          try {
            // 获取标签页详细信息
            const tab = await chrome.tabs.get(chromeIdResult.data);
            if (tab) {
              relatedTabs.push({
                id: tab.id!,
                url: tab.url!,
                title: tab.title!,
                isPinned: tab.pinned,
                isActive: tab.active,
                windowId: tab.windowId,
                index: tab.index
              });
              validWorkonaIds.push(workonaId);
              console.log(`✅ 找到 Workona 管理的标签页: ${tab.title} (${workonaId})`);
            }
          } catch (tabError) {
            // 标签页不存在，清理无效映射
            await WorkonaTabManager.removeTabMapping(workonaId);
            console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
          }
        } else {
          // 映射无效，清理
          await WorkonaTabManager.removeTabMapping(workonaId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
        }
      }

      console.log(`📊 工作区 "${workspace.name}" Workona 管理的标签页: ${relatedTabs.length} 个 (有效映射: ${validWorkonaIds.length})`);
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error('获取工作区相关标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);

      // 只在当前窗口中查找非相关标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      const nonRelatedTabs = currentTabs.filter(tab =>
        !workspaceUrls.some(url => tab.url.startsWith(url))
      );

      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get non-workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId: number): Promise<boolean> {
    try {
      // 概念性重构：通过 Workona ID 映射和元数据检查
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        // 无 Workona ID = 系统标签页或未分类标签页
        return true;
      }

      // 检查标签页性质
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return true;
      }

      // 根据来源判断是否为用户手动打开
      const { metadata } = metadataResult.data;
      return metadata?.source === 'user_opened';
    } catch {
      // 出错时采用保守策略，认为是用户标签页
      return true;
    }
  }

  /**
   * 获取当前应该用于新标签页的工作区（避免循环依赖）
   */
  private static async getCurrentWorkspaceForNewTab(): Promise<WorkSpace | null> {
    try {
      // 策略1：检查当前窗口中其他标签页的工作区归属
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        return null;
      }

      const currentTabs = currentTabsResult.data!;

      // 查找当前窗口中有 Workona ID 的标签页
      for (const tab of currentTabs) {
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceId = workonaIdResult.data.split('-')[1];

          // 获取工作区信息
          const { StorageManager } = await import('./storage');
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success && workspacesResult.data) {
            const workspace = workspacesResult.data.find(w => w.id === workspaceId);
            if (workspace) {
              console.log(`🎯 通过窗口中其他标签页检测到当前工作区: ${workspace.name}`);
              return workspace;
            }
          }
        }
      }

      // 策略2：使用默认工作区或第一个工作区
      const { StorageManager } = await import('./storage');
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data && workspacesResult.data.length > 0) {
        const defaultWorkspace = workspacesResult.data[0]; // 使用第一个工作区作为默认
        console.log(`🎯 使用默认工作区: ${defaultWorkspace.name}`);
        return defaultWorkspace;
      }

      return null;
    } catch (error) {
      console.error('获取当前工作区失败:', error);
      return null;
    }
  }

  /**
   * 自动为新标签页创建会话临时 Workona ID（概念性重构）
   */
  static async autoClassifyNewTab(tabId: number, url: string): Promise<OperationResult<void>> {
    try {
      console.log(`🎯 开始自动分类标签页: ID=${tabId}, URL=${url}`);

      // 排除系统页面
      if (url.includes('chrome://') ||
          url.includes('chrome-extension://') ||
          url.includes('edge://') ||
          url.includes('about:')) {
        console.log(`🚫 跳过系统页面: ${url}`);
        return { success: true }; // 系统页面不需要分类
      }

      // 检查是否已有 Workona ID
      const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      console.log(`🔍 检查现有 Workona ID:`, existingResult);

      if (existingResult.success && existingResult.data) {
        console.log(`ℹ️ 标签页已有 Workona ID: ${existingResult.data}，跳过分类`);
        return { success: true }; // 已有映射，无需重复创建
      }

      // 获取当前活跃工作区（避免循环依赖）
      const activeWorkspace = await this.getCurrentWorkspaceForNewTab();
      console.log(`🔍 检查当前工作区:`, activeWorkspace);

      if (!activeWorkspace) {
        console.log(`ℹ️ 无当前工作区，跳过标签页自动分类: ${url}`);
        return { success: true };
      }
      console.log(`✅ 找到活跃工作区: ${activeWorkspace.name} (ID: ${activeWorkspace.id})`);

      // 为用户临时打开的标签页创建会话临时 Workona ID
      const workonaId = WorkonaTabManager.generateWorkonaTabId(activeWorkspace.id);
      console.log(`🆔 生成 Workona ID: ${workonaId}`);

      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tabId,
        activeWorkspace.id,
        undefined, // 无对应的网站配置ID
        {
          isWorkspaceCore: false, // 标记为会话临时标签页
          source: 'user_opened'
        }
      );

      console.log(`🔍 创建映射结果:`, mappingResult);

      if (mappingResult.success) {
        console.log(`✨ 自动为用户标签页创建会话临时 Workona ID: ${workonaId} (${url})`);
      } else {
        console.error(`❌ 创建 Workona ID 映射失败:`, mappingResult.error);
      }

      return mappingResult;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to auto-classify new tab',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前工作区中用户自行打开的标签页（Workona 风格：基于 ID 映射）
   */
  static async getUserOpenedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`🔍 查找工作区 "${workspace.name}" 中用户自行打开的标签页 (Workona 风格)`);

      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const userOpenedTabs: TabInfo[] = [];

      // 通过 Workona ID 映射检查每个标签页
      for (const tab of currentTabs) {
        // 排除Chrome内部页面和扩展页面
        if (tab.url.startsWith('chrome://') ||
            tab.url.startsWith('chrome-extension://') ||
            tab.url.startsWith('edge://') ||
            tab.url.startsWith('about:')) {
          continue;
        }

        // 检查是否有 Workona ID 映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          // 没有 Workona ID 映射，认为是用户自行打开的标签页
          userOpenedTabs.push(tab);
          console.log(`👤 发现用户自行打开的标签页: ${tab.title} (${tab.url})`);
        } else {
          console.log(`🏢 跳过 Workona 管理的标签页: ${tab.title} (${workonaIdResult.data})`);
        }
      }

      console.log(`📊 工作区 "${workspace.name}" 中用户自行打开的标签页共 ${userOpenedTabs.length} 个`);
      return { success: true, data: userOpenedTabs };
    } catch (error) {
      console.error('获取用户自行打开的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get user opened tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前工作区中配置的标签页（工作区设置中明确添加的网站标签页）
   */
  static async getWorkspaceConfiguredTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      // 过滤出属于工作区配置的标签页
      const configuredTabs = currentTabs.filter(tab => {
        return workspaceUrls.some(url => tab.url.startsWith(url));
      });

      console.log(`工作区 "${workspace.name}" 中配置的标签页共 ${configuredTabs.length} 个`);
      return { success: true, data: configuredTabs };
    } catch (error) {
      console.error('获取工作区配置的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace configured tabs',
          details: error,
        },
      };
    }
  }

  // ===== Workona 风格标签页管理方法 =====

  /**
   * 创建 Workona 风格标签页
   */
  static async createWorkonaTab(options: {
    workspaceId: string;
    url: string;
    title?: string;
    isPinned?: boolean;
    isActive?: boolean;
    websiteId?: string;
  }): Promise<OperationResult<{ tabInfo: TabInfo; workonaId: string }>> {
    try {
      console.log(`🚀 创建 Workona 标签页: ${options.url} (工作区: ${options.workspaceId})`);

      // 生成 Workona ID
      const workonaId = WorkonaTabManager.generateWorkonaTabId(options.workspaceId);

      // 创建 Chrome 标签页
      const tab = await chrome.tabs.create({
        url: options.url,
        pinned: options.isPinned ?? true,
        active: options.isActive ?? false,
      });

      if (!tab.id) {
        throw new Error('Failed to create Chrome tab');
      }

      // 创建标签页信息
      const tabInfo: TabInfo = {
        id: tab.id,
        url: tab.url || options.url,
        title: tab.title || options.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      // 创建 ID 映射
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tab.id,
        options.workspaceId,
        options.websiteId
      );

      if (!mappingResult.success) {
        // 如果映射创建失败，尝试关闭标签页
        try {
          await chrome.tabs.remove(tab.id);
        } catch (cleanupError) {
          console.warn('清理失败的标签页时出错:', cleanupError);
        }
        return { success: false, error: mappingResult.error };
      }

      console.log(`✅ 成功创建 Workona 标签页: ${workonaId} <-> ${tab.id}`);
      return {
        success: true,
        data: { tabInfo, workonaId }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create Workona tab',
          details: error,
        },
      };
    }
  }

  /**
   * 获取标签页的 Workona ID
   */
  static async getTabWorkonaId(chromeTabId: number): Promise<OperationResult<string | null>> {
    return await WorkonaTabManager.getWorkonaIdByChromeId(chromeTabId);
  }

  /**
   * 通过 Workona ID 获取标签页信息
   */
  static async getTabByWorkonaId(workonaId: string): Promise<OperationResult<TabInfo | null>> {
    try {
      const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
      if (!chromeIdResult.success || !chromeIdResult.data) {
        return { success: true, data: null };
      }

      const chromeId = chromeIdResult.data;

      try {
        const tab = await chrome.tabs.get(chromeId);
        const tabInfo: TabInfo = {
          id: tab.id!,
          url: tab.url || '',
          title: tab.title || '',
          favicon: tab.favIconUrl || '',
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
        };

        return { success: true, data: tabInfo };
      } catch (tabError) {
        // 标签页不存在，清理映射
        await WorkonaTabManager.removeTabMapping(workonaId);
        return { success: true, data: null };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tab by Workona ID',
          details: error,
        },
      };
    }
  }

  /**
   * 同步工作区的 Workona 标签页映射
   */
  static async syncWorkspaceTabMappings(workspaceId: string): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 同步工作区 ${workspaceId} 的标签页映射...`);

      // 获取工作区的所有 Workona 标签页ID
      const workonaIdsResult = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);
      if (!workonaIdsResult.success) {
        return { success: false, error: workonaIdsResult.error };
      }

      const workonaIds = workonaIdsResult.data!;
      let syncedCount = 0;
      let cleanedCount = 0;

      // 检查每个 Workona 标签页是否仍然存在
      for (const workonaId of workonaIds) {
        const tabResult = await this.getTabByWorkonaId(workonaId);
        if (!tabResult.success) {
          continue;
        }

        if (tabResult.data) {
          syncedCount++;
        } else {
          cleanedCount++;
        }
      }

      console.log(`✅ 工作区 ${workspaceId} 标签页映射同步完成: ${syncedCount} 个有效, ${cleanedCount} 个已清理`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to sync workspace tab mappings',
          details: error,
        },
      };
    }
  }
}



/**
 * 工作区标签页内容匹配系统
 */
export class WorkspaceTabContentMatcher {


  /**
   * 检查标签页是否与工作区网站匹配（Workona 风格：完全基于 ID 映射和 URL 匹配）
   */
  static async isWorkspaceTab(tab: TabInfo): Promise<WorkonaTabMatchResult> {
    try {
      // 排除系统页面和扩展页面
      if (tab.url.includes('workspace-placeholder.html') ||
          tab.url.includes('chrome-extension://') ||
          tab.url.includes('chrome://') ||
          tab.url.includes('edge://') ||
          tab.url.includes('about:')) {
        return {
          isMatch: false,
          confidence: 0,
          matchType: 'none'
        };
      }

      // 阶段1：优先通过 Workona ID 映射查找（最高优先级）
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
      let workonaTabId: string | undefined;

      if (workonaIdResult.success && workonaIdResult.data) {
        workonaTabId = workonaIdResult.data;
        // 从 Workona ID 中提取工作区ID：t-{workspaceId}-{uuid}
        const workspaceId = workonaTabId.split('-')[1];

        console.log(`🔗 通过 Workona ID 映射找到工作区标签页: ${tab.url} -> ${workonaTabId} (工作区: ${workspaceId})`);

        // 验证工作区是否仍然存在
        const workspaceWebsites = await this.getAllWorkspaceWebsitesWithDetails();
        const matchingWebsite = workspaceWebsites.find(w => w.workspaceId === workspaceId);

        if (matchingWebsite) {
          return {
            isMatch: true,
            workspaceId: workspaceId,
            websiteId: matchingWebsite.websiteId,
            workonaTabId,
            confidence: 1.0, // 最高置信度
            matchType: 'exact'
          };
        } else {
          // 工作区不存在，清理无效映射
          await WorkonaTabManager.removeTabMapping(workonaTabId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaTabId}`);
        }
      }

      // 阶段2：基于 URL 匹配 + 自动创建 Workona ID 映射
      const workspaceWebsites = await this.getAllWorkspaceWebsitesWithDetails();

      for (const website of workspaceWebsites) {
        if (this.isUrlMatch(tab.url, website.url)) {
          console.log(`🌐 通过 URL 匹配识别为工作区标签页: ${tab.url} -> 工作区: ${website.workspaceId}`);

          // 自动创建 Workona ID 映射（概念性重构：根据网站配置判断类型）
          const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(website.workspaceId);
          const mappingResult = await WorkonaTabManager.createTabIdMapping(
            newWorkonaId,
            tab.id,
            website.workspaceId,
            website.websiteId,
            {
              isWorkspaceCore: true, // 匹配工作区网站配置 = 核心标签页
              source: 'workspace_website'
            }
          );

          if (mappingResult.success) {
            workonaTabId = newWorkonaId;
            console.log(`✨ 为工作区网站自动创建核心 Workona ID: ${workonaTabId}`);
          }

          return {
            isMatch: true,
            workspaceId: website.workspaceId,
            websiteId: website.websiteId,
            workonaTabId,
            confidence: 0.9, // 高置信度
            matchType: 'domain'
          };
        }
      }

      return {
        isMatch: false,
        confidence: 0,
        matchType: 'none'
      };
    } catch (error) {
      console.error('检查工作区标签页匹配失败:', error);
      return {
        isMatch: false,
        confidence: 0,
        matchType: 'none'
      };
    }
  }

  /**
   * 简单的URL匹配：使用前缀匹配
   */
  private static isUrlMatch(tabUrl: string, websiteUrl: string): boolean {
    try {
      // 标准化URL，移除末尾的斜杠
      const normalizeUrl = (url: string) => url.replace(/\/$/, '');

      const normalizedTabUrl = normalizeUrl(tabUrl);
      const normalizedWebsiteUrl = normalizeUrl(websiteUrl);

      // 使用前缀匹配
      return normalizedTabUrl.startsWith(normalizedWebsiteUrl);
    } catch (error) {
      console.error('URL匹配失败:', error);
      return false;
    }
  }



  /**
   * 获取所有工作区网站的详细信息（简化版）
   */
  private static async getAllWorkspaceWebsitesWithDetails(): Promise<Array<{
    url: string;
    workspaceId: string;
    websiteId: string;
  }>> {
    try {
      const { StorageManager } = await import('./storage');
      const workspacesResult = await StorageManager.getWorkspaces();

      if (!workspacesResult.success || !workspacesResult.data) {
        return [];
      }

      const websites: Array<{
        url: string;
        workspaceId: string;
        websiteId: string;
      }> = [];

      for (const workspace of workspacesResult.data) {
        for (const website of workspace.websites) {
          websites.push({
            url: website.url,
            workspaceId: workspace.id,
            websiteId: website.id
          });
        }
      }

      return websites;
    } catch (error) {
      console.error('获取工作区网站详细信息失败:', error);
      return [];
    }
  }


}

/**
 * 全局用户标签页隐藏/显示管理类
 */
export class GlobalUserTabsVisibilityManager {


  /**
   * 检查标签页是否为真正的用户标签页（非工作区管理的标签页）
   * 简化版：基于固定状态的识别
   */
  /**
   * 检查是否为新标签页
   */
  private static isNewTabPage(tab: TabInfo): boolean {
    return tab.url === 'chrome://newtab/' || tab.url === 'chrome://new-tab-page/' ||
           tab.url === 'about:newtab' || tab.url === 'edge://newtab/' ||
           (tab.url && tab.url.startsWith('chrome://newtab')) ||
           (tab.title === 'New Tab' || tab.title === '新标签页' || tab.title === 'Neuer Tab');
  }

  static async isRealUserTab(tab: TabInfo): Promise<boolean> {
    try {
      console.log(`🔍 检查标签页: ${tab.title} (${tab.url}) - ID: ${tab.id}`);

      // 排除新标签页（空白标签页）
      if (this.isNewTabPage(tab)) {
        console.log(`🚫 排除新标签页: ${tab.url} (${tab.title})`);
        return false;
      }

      // 概念性重构：基于 isWorkspaceCore 标记进行精确判断
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
      console.log(`🔍 Workona ID 查询结果:`, workonaIdResult);

      if (!workonaIdResult.success || !workonaIdResult.data) {
        // 无 Workona ID = 系统标签页 = 不参与隐藏逻辑
        console.log(`🔧 识别为系统标签页（无 Workona ID）: ${tab.url} (${tab.title})`);
        return false;
      }

      // 检查 Workona ID 的性质：核心标签页 vs 临时标签页
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      console.log(`🔍 元数据查询结果:`, metadataResult);

      if (!metadataResult.success || !metadataResult.data) {
        // 映射损坏，采用保守策略
        console.log(`⚠️ Workona ID 映射损坏: ${workonaIdResult.data}`);
        return false;
      }

      const { isWorkspaceCore, tabType } = metadataResult.data;
      console.log(`🔍 标签页分类: isWorkspaceCore=${isWorkspaceCore}, tabType=${tabType}`);

      if (isWorkspaceCore) {
        // 工作区核心标签页 = 不受隐藏影响
        console.log(`🏢 识别为工作区核心标签页: ${tab.url} (${tab.title}) - Workona ID: ${workonaIdResult.data}`);
        return false;
      } else {
        // 会话临时标签页 = 可被隐藏
        console.log(`👤 识别为会话临时标签页: ${tab.url} (${tab.title}) - 类型: ${tabType}`);
        return true;
      }
    } catch (error) {
      console.error('检查用户标签页失败:', error);
      // 出错时采用保守策略，不参与隐藏
      return false;
    }
  }


  /**
   * 获取全局用户标签页隐藏状态
   */
  static async getGlobalUserTabsState(): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    totalUserTabs: number;
    visibleUserTabs: number;
    canContinueHiding: boolean; // 是否可以继续隐藏
    actionType: 'hide' | 'continue_hide' | 'show'; // 建议的操作类型
  }>> {
    try {
      // 从存储中获取全局隐藏状态
      const result = await chrome.storage.local.get(['globalUserTabsHidden', 'globalHiddenTabIds']);
      const hiddenTabIds = result.globalHiddenTabIds || [];

      // 验证隐藏的标签页是否仍然存在
      const validHiddenTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          validHiddenTabIds.push(tabId);
        } catch {
          // 标签页已不存在，忽略
        }
      }

      // 如果有无效的隐藏标签页，更新存储
      if (validHiddenTabIds.length !== hiddenTabIds.length) {
        await chrome.storage.local.set({
          globalHiddenTabIds: validHiddenTabIds
        });

        // 如果没有有效的隐藏标签页，清除隐藏状态
        if (validHiddenTabIds.length === 0) {
          await chrome.storage.local.set({
            globalUserTabsHidden: false
          });
        }
      }

      // 获取当前所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      // 计算真正的用户标签页（排除工作区管理的标签页）
      const allTabs = allTabsResult.data!;
      const allUserTabs = [];

      console.log(`🔍 开始分析 ${allTabs.length} 个标签页，识别用户标签页...`);

      for (const tab of allTabs) {
        const isUserTab = await this.isRealUserTab(tab);
        if (isUserTab) {
          allUserTabs.push(tab);
          console.log(`✅ 识别为用户标签页: ${tab.title} (${tab.url})`);
        } else {
          console.log(`❌ 非用户标签页: ${tab.title} (${tab.url})`);
        }
      }

      console.log(`📊 用户标签页识别结果: 总计 ${allUserTabs.length} 个用户标签页`);
      if (allUserTabs.length > 0) {
        console.log('用户标签页列表:', allUserTabs.map(tab => `${tab.title} (${tab.url})`));
      }

      // 计算可见的用户标签页（在主窗口中的）
      const currentWindow = await chrome.windows.getCurrent();
      const visibleUserTabs = allUserTabs.filter(tab =>
        tab.windowId === currentWindow.id &&
        !validHiddenTabIds.includes(tab.id)
      );

      // 总用户标签页数量 = 可见的 + 隐藏的
      const totalUserTabs = visibleUserTabs.length + validHiddenTabIds.length;

      // 判断操作类型
      const hasHiddenTabs = validHiddenTabIds.length > 0;
      const hasVisibleTabs = visibleUserTabs.length > 0;

      let actionType: 'hide' | 'continue_hide' | 'show';
      let canContinueHiding = false;

      if (hasHiddenTabs && hasVisibleTabs) {
        // 既有隐藏的又有可见的 -> 继续隐藏
        actionType = 'continue_hide';
        canContinueHiding = true;
      } else if (hasHiddenTabs && !hasVisibleTabs) {
        // 只有隐藏的，没有可见的 -> 显示
        actionType = 'show';
        canContinueHiding = false;
      } else {
        // 没有隐藏的，只有可见的 -> 隐藏
        actionType = 'hide';
        canContinueHiding = false;
      }

      return {
        success: true,
        data: {
          isHidden: hasHiddenTabs,
          hiddenTabIds: validHiddenTabIds,
          totalUserTabs,
          visibleUserTabs: visibleUserTabs.length,
          canContinueHiding,
          actionType,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get global user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 设置全局用户标签页隐藏状态
   */
  static async setGlobalUserTabsState(isHidden: boolean, hiddenTabIds: number[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        globalUserTabsHidden: isHidden,
        globalHiddenTabIds: hiddenTabIds,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set global user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 继续隐藏当前可见的用户标签页（保留已隐藏的标签页）
   */
  static async continueHideUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔒 继续隐藏当前可见的用户标签页');

      // 获取当前状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const currentState = stateResult.data!;

      // 获取所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      const allTabs = allTabsResult.data!;
      const currentWindow = await chrome.windows.getCurrent();

      // 筛选出当前窗口中的可见用户标签页
      const visibleUserTabs = [];

      for (const tab of allTabs) {
        if (tab.windowId === currentWindow.id &&
            await this.isRealUserTab(tab) &&
            !currentState.hiddenTabIds.includes(tab.id)) {
          visibleUserTabs.push(tab);
        }
      }

      if (visibleUserTabs.length === 0) {
        console.log('⚠️ 没有新的用户标签页需要隐藏');
        return { success: true, data: [] };
      }

      console.log(`📤 准备继续隐藏 ${visibleUserTabs.length} 个可见的用户标签页`);

      // 移动新的可见标签页到隐藏窗口
      const { WindowManager } = await import('./windowManager');
      const newTabIds = visibleUserTabs.map(tab => tab.id).filter(id => id && typeof id === 'number' && id > 0);

      // 安全性检查：确保有有效的标签页ID
      if (newTabIds.length === 0) {
        console.warn('⚠️ 没有有效的标签页ID可以继续隐藏');
        return { success: true, data: [] };
      }

      if (newTabIds.length !== visibleUserTabs.length) {
        console.warn(`⚠️ 发现 ${visibleUserTabs.length - newTabIds.length} 个无效的标签页ID，已过滤`);
      }

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        newTabIds,
        'global-hidden-tabs',
        '隐藏的用户标签页'
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到隐藏窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 更新隐藏状态，合并新的标签页ID
      const allHiddenTabIds = [...currentState.hiddenTabIds, ...newTabIds];
      const saveResult = await this.setGlobalUserTabsState(true, allHiddenTabIds);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error,
        };
      }

      console.log(`✅ 成功继续隐藏 ${newTabIds.length} 个用户标签页`);
      return { success: true, data: newTabIds };
    } catch (error) {
      console.error('❌ 继续隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to continue hiding user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 全局隐藏所有用户标签页
   */
  static async hideAllUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔒 开始全局隐藏所有用户标签页');

      // 获取所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      const allTabs = allTabsResult.data!;

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 筛选出当前窗口中的真正用户标签页（排除工作区管理的标签页）
      const userTabs = [];

      for (const tab of allTabs) {
        if (tab.windowId === currentWindow.id && await this.isRealUserTab(tab)) {
          userTabs.push(tab);
        }
      }

      if (userTabs.length === 0) {
        console.log('⚠️ 没有用户标签页需要隐藏');
        return { success: true, data: [] };
      }

      // 检查是否有固定标签页
      const pinnedTabs = allTabs.filter(tab =>
        tab.windowId === currentWindow.id && tab.isPinned
      );

      // 检查当前窗口中已有的新标签页数量
      const existingNewTabs = allTabs.filter(tab =>
        tab.windowId === currentWindow.id && this.isNewTabPage(tab)
      );

      // 检查是否需要创建新标签页：只有一个用户标签页且不是新标签页，且当前没有新标签页
      const shouldCreateNewTab = userTabs.length === 1 &&
                                !this.isNewTabPage(userTabs[0]) &&
                                existingNewTabs.length === 0;

      // 如果只有一个用户标签页且不是新标签页，且没有现有新标签页，创建一个新标签页以避免窗口被关闭
      if (shouldCreateNewTab) {
        const reason = pinnedTabs.length === 0
          ? '没有固定标签页且只剩一个用户标签页'
          : '有固定标签页但只剩一个用户标签页';
        console.log(`📌 ${reason}，且没有现有新标签页，检查是否需要创建新标签页`);

        // 再次检查是否已有新标签页（防重复）
        const recentTabs = await chrome.tabs.query({ currentWindow: true });
        const hasRecentNewTab = recentTabs.some(tab =>
          tab.url === 'chrome://newtab/' ||
          tab.url === 'about:blank' ||
          tab.url === 'edge://newtab/' ||
          !tab.url ||
          tab.url === ''
        );

        if (!hasRecentNewTab) {
          try {
            await chrome.tabs.create({
              url: 'chrome://newtab/',
              active: true
            });
            console.log('📌 已创建新标签页');
          } catch (error) {
            console.warn('创建新标签页失败:', error);
          }
        } else {
          console.log('📌 检测到已有新标签页，跳过创建');
        }
      } else if (userTabs.length === 1 && !this.isNewTabPage(userTabs[0]) && existingNewTabs.length > 0) {
        console.log(`📌 只有一个用户标签页，但已存在 ${existingNewTabs.length} 个新标签页，无需重复创建`);
      }

      console.log(`📤 准备隐藏 ${userTabs.length} 个用户标签页（自动排除新标签页）`);

      // 创建全局隐藏窗口
      const { WindowManager } = await import('./windowManager');
      const tabIds = userTabs.map(tab => tab.id).filter(id => id && typeof id === 'number' && id > 0);

      // 安全性检查：确保有有效的标签页ID
      if (tabIds.length === 0) {
        console.warn('⚠️ 没有有效的标签页ID可以隐藏');
        return { success: true, data: [] };
      }

      if (tabIds.length !== userTabs.length) {
        console.warn(`⚠️ 发现 ${userTabs.length - tabIds.length} 个无效的标签页ID，已过滤`);
      }

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        'global-hidden-tabs',
        '隐藏的用户标签页'
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到隐藏窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 保存全局隐藏状态
      const saveResult = await this.setGlobalUserTabsState(true, tabIds);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error,
        };
      }

      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error('❌ 全局隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide all user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 全局显示所有隐藏的用户标签页
   */
  static async showAllUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔓 开始全局显示所有隐藏的用户标签页');

      // 获取隐藏状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log('⚠️ 没有隐藏的用户标签页需要显示');
        return { success: true, data: [] };
      }

      console.log(`📥 准备显示 ${hiddenTabIds.length} 个隐藏的用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log('⚠️ 所有隐藏的标签页都已不存在');
        await this.setGlobalUserTabsState(false, []);
        return { success: true, data: [] };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 移动隐藏的标签页回到主窗口
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id!,
        index: -1 // 移动到窗口末尾
      });

      // 清除全局隐藏状态
      const clearResult = await this.setGlobalUserTabsState(false, []);
      if (!clearResult.success) {
        return {
          success: false,
          error: clearResult.error,
        };
      }

      console.log(`✅ 成功显示 ${existingTabIds.length} 个隐藏的用户标签页`);
      return { success: true, data: existingTabIds };
    } catch (error) {
      console.error('❌ 全局显示用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show all user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 切换全局用户标签页的显示/隐藏状态（智能模式）
   */
  static async toggleGlobalUserTabsVisibility(): Promise<OperationResult<{
    action: 'hidden' | 'shown' | 'continue_hidden';
    tabIds: number[];
  }>> {
    try {
      console.log('🔄 智能切换全局用户标签页的显示状态');

      // 获取当前状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { actionType } = stateResult.data!;

      switch (actionType) {
        case 'show':
          // 显示所有隐藏的标签页
          const showResult = await this.showAllUserTabs();
          if (!showResult.success) {
            return {
              success: false,
              error: showResult.error,
            };
          }

          console.log(`✅ 全局用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'shown',
              tabIds: showResult.data!,
            },
          };

        case 'continue_hide':
          // 继续隐藏当前可见的标签页
          const continueHideResult = await this.continueHideUserTabs();
          if (!continueHideResult.success) {
            return {
              success: false,
              error: continueHideResult.error,
            };
          }

          console.log(`✅ 继续隐藏用户标签页成功，影响 ${continueHideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'continue_hidden',
              tabIds: continueHideResult.data!,
            },
          };

        case 'hide':
        default:
          // 隐藏所有用户标签页
          const hideResult = await this.hideAllUserTabs();
          if (!hideResult.success) {
            return {
              success: false,
              error: hideResult.error,
            };
          }

          console.log(`✅ 全局用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'hidden',
              tabIds: hideResult.data!,
            },
          };
      }
    } catch (error) {
      console.error('❌ 切换全局用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle global user tabs visibility',
          details: error,
        },
      };
    }
  }
}

/**
 * 用户标签页隐藏/显示管理类（保留向后兼容性）
 * @deprecated 请使用 GlobalUserTabsVisibilityManager
 */
export class UserTabsVisibilityManager {
  /**
   * 隐藏当前工作区中用户自行打开的标签页
   */
  static async hideUserTabs(workspace: WorkSpace): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔒 开始隐藏工作区 "${workspace.name}" 中的用户标签页`);

      // 获取用户自行打开的标签页
      const userTabsResult = await TabManager.getUserOpenedTabs(workspace);
      if (!userTabsResult.success) {
        return {
          success: false,
          error: userTabsResult.error,
        };
      }

      const userTabs = userTabsResult.data!;
      if (userTabs.length === 0) {
        console.log('⚠️ 没有用户自行打开的标签页需要隐藏');
        return { success: true, data: [] };
      }

      console.log(`📤 准备隐藏 ${userTabs.length} 个用户标签页`);

      // 移动用户标签页到专用窗口
      const tabIds = userTabs.map(tab => tab.id);
      const { WindowManager } = await import('./windowManager');

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        `${workspace.name} - 隐藏的用户标签页`
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到专用窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 更新工作区的隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const updateResult = await WorkspaceManager.setUserTabsHiddenState(
        workspace.id,
        true,
        tabIds
      );

      if (!updateResult.success) {
        console.error('❌ 更新工作区隐藏状态失败:', updateResult.error);
        return {
          success: false,
          error: updateResult.error,
        };
      }

      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error('❌ 隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 显示当前工作区中已隐藏的用户标签页
   */
  static async showUserTabs(workspace: WorkSpace): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔓 开始显示工作区 "${workspace.name}" 中的隐藏用户标签页`);

      // 获取隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log('⚠️ 没有隐藏的用户标签页需要显示');
        return { success: true, data: [] };
      }

      console.log(`📥 准备显示 ${hiddenTabIds.length} 个隐藏的用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log('⚠️ 所有隐藏的标签页都已不存在');
        // 清理隐藏状态
        await WorkspaceManager.setUserTabsHiddenState(workspace.id, false, []);
        return { success: true, data: [] };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 处理固定标签页：先取消固定，移动后重新固定
      const tabsToRepin: number[] = [];

      for (const tabId of existingTabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab.pinned) {
            // 记录需要重新固定的标签页
            tabsToRepin.push(tabId);
            // 先取消固定
            await chrome.tabs.update(tabId, { pinned: false });
            console.log(`📌 取消固定标签页 ${tabId} 以便移动`);
          }
        } catch (error) {
          console.warn(`检查标签页 ${tabId} 固定状态失败:`, error);
        }
      }

      // 移动隐藏的标签页回到主窗口
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id!,
        index: -1 // 移动到窗口末尾
      });

      // 重新固定之前固定的标签页
      for (const tabId of tabsToRepin) {
        try {
          await chrome.tabs.update(tabId, { pinned: true });
          console.log(`📌 重新固定标签页 ${tabId}`);
        } catch (error) {
          console.warn(`重新固定标签页 ${tabId} 失败:`, error);
        }
      }

      // 更新工作区的隐藏状态
      const updateResult = await WorkspaceManager.setUserTabsHiddenState(
        workspace.id,
        false,
        []
      );

      if (!updateResult.success) {
        console.error('❌ 更新工作区隐藏状态失败:', updateResult.error);
        return {
          success: false,
          error: updateResult.error,
        };
      }

      // 如果有标签页已不存在，清理记录
      const removedTabIds = hiddenTabIds.filter(id => !existingTabIds.includes(id));
      if (removedTabIds.length > 0) {
        await WorkspaceManager.clearHiddenTabIds(workspace.id, removedTabIds);
      }

      console.log(`✅ 成功显示 ${existingTabIds.length} 个隐藏的用户标签页`);
      return { success: true, data: existingTabIds };
    } catch (error) {
      console.error('❌ 显示用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 切换用户标签页的显示/隐藏状态
   */
  static async toggleUserTabsVisibility(workspace: WorkSpace): Promise<OperationResult<{
    action: 'hidden' | 'shown';
    tabIds: number[];
  }>> {
    try {
      console.log(`🔄 切换工作区 "${workspace.name}" 用户标签页的显示状态`);

      // 获取当前隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden } = stateResult.data!;

      if (isHidden) {
        // 当前是隐藏状态，执行显示操作
        const showResult = await this.showUserTabs(workspace);
        if (!showResult.success) {
          return {
            success: false,
            error: showResult.error,
          };
        }

        // 显示操作完成后，确保状态正确更新
        console.log(`✅ 用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'shown',
            tabIds: showResult.data!,
          },
        };
      } else {
        // 当前是显示状态，执行隐藏操作
        const hideResult = await this.hideUserTabs(workspace);
        if (!hideResult.success) {
          return {
            success: false,
            error: hideResult.error,
          };
        }

        // 隐藏操作完成后，确保状态正确更新
        console.log(`✅ 用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'hidden',
            tabIds: hideResult.data!,
          },
        };
      }
    } catch (error) {
      console.error('❌ 切换用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 获取用户标签页的当前显示状态
   */
  static async getUserTabsVisibilityState(workspace: WorkSpace): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabsCount: number;
    userTabsCount: number;
  }>> {
    try {
      // 获取隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      // 获取当前用户标签页数量
      const userTabsResult = await TabManager.getUserOpenedTabs(workspace);
      const userTabsCount = userTabsResult.success ? userTabsResult.data!.length : 0;

      return {
        success: true,
        data: {
          isHidden,
          hiddenTabsCount: hiddenTabIds.length,
          userTabsCount,
        },
      };
    } catch (error) {
      console.error('❌ 获取用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get user tabs visibility state',
          details: error,
        },
      };
    }
  }
}

/**
 * 工作区级别的用户标签页可见性管理器
 * 管理特定工作区中的用户标签页隐藏和显示
 */
export class WorkspaceUserTabsVisibilityManager {
  /**
   * 获取工作区用户标签页状态
   */
  static async getWorkspaceUserTabsState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    totalUserTabs: number;
    visibleUserTabs: number;
    canContinueHiding: boolean;
    actionType: 'hide' | 'continue_hide' | 'show';
  }>> {
    try {
      // 从存储中获取工作区隐藏状态
      const result = await chrome.storage.local.get([`workspaceUserTabsHidden_${workspaceId}`, `workspaceHiddenTabIds_${workspaceId}`]);
      const hiddenTabIds = result[`workspaceHiddenTabIds_${workspaceId}`] || [];

      // 验证隐藏的标签页是否仍然存在
      const validHiddenTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          validHiddenTabIds.push(tabId);
        } catch {
          // 标签页已不存在，忽略
        }
      }

      // 如果有无效的隐藏标签页，更新存储
      if (validHiddenTabIds.length !== hiddenTabIds.length) {
        await chrome.storage.local.set({
          [`workspaceHiddenTabIds_${workspaceId}`]: validHiddenTabIds
        });

        // 如果没有有效的隐藏标签页，清除隐藏状态
        if (validHiddenTabIds.length === 0) {
          await chrome.storage.local.set({
            [`workspaceUserTabsHidden_${workspaceId}`]: false
          });
        }
      }

      // 获取当前所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      // 计算工作区中的用户标签页
      const allTabs = allTabsResult.data!;
      const workspaceUserTabs = [];

      console.log(`🔍 开始分析工作区 ${workspaceId} 的用户标签页...`);

      for (const tab of allTabs) {
        // 检查是否是用户标签页
        const isUserTab = await GlobalUserTabsVisibilityManager.isRealUserTab(tab);
        if (!isUserTab) {
          continue;
        }

        // 检查是否属于当前工作区
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceIdFromMapping = workonaIdResult.data.split('-')[1];
          if (workspaceIdFromMapping === workspaceId) {
            workspaceUserTabs.push(tab);
            console.log(`✅ 找到工作区用户标签页: ${tab.title} (${tab.url})`);
          }
        }
      }

      // 计算可见的用户标签页（在主窗口中的）
      const currentWindow = await chrome.windows.getCurrent();
      const visibleUserTabs = workspaceUserTabs.filter(tab =>
        tab.windowId === currentWindow.id &&
        !validHiddenTabIds.includes(tab.id)
      );

      // 总用户标签页数量 = 可见的 + 隐藏的
      const totalUserTabs = visibleUserTabs.length + validHiddenTabIds.length;

      // 判断操作类型
      const hasHiddenTabs = validHiddenTabIds.length > 0;
      const hasVisibleTabs = visibleUserTabs.length > 0;

      let actionType: 'hide' | 'continue_hide' | 'show';
      let canContinueHiding = false;

      if (hasHiddenTabs && hasVisibleTabs) {
        // 既有隐藏的又有可见的 -> 继续隐藏
        actionType = 'continue_hide';
        canContinueHiding = true;
      } else if (hasHiddenTabs && !hasVisibleTabs) {
        // 只有隐藏的，没有可见的 -> 显示
        actionType = 'show';
        canContinueHiding = false;
      } else {
        // 没有隐藏的，只有可见的 -> 隐藏
        actionType = 'hide';
        canContinueHiding = false;
      }

      console.log(`📊 工作区 ${workspaceId} 用户标签页状态: 总计 ${totalUserTabs} 个，可见 ${visibleUserTabs.length} 个，隐藏 ${validHiddenTabIds.length} 个`);

      return {
        success: true,
        data: {
          isHidden: hasHiddenTabs,
          hiddenTabIds: validHiddenTabIds,
          totalUserTabs,
          visibleUserTabs: visibleUserTabs.length,
          canContinueHiding,
          actionType,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspace user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 切换工作区用户标签页的显示/隐藏状态
   */
  static async toggleWorkspaceUserTabsVisibility(workspace: WorkSpace): Promise<OperationResult<{
    action: 'hidden' | 'shown';
    tabIds: number[];
  }>> {
    try {
      console.log(`🔄 切换工作区 "${workspace.name}" 用户标签页的显示状态`);

      // 获取当前状态
      const stateResult = await this.getWorkspaceUserTabsState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { actionType } = stateResult.data!;

      switch (actionType) {
        case 'show':
          // 显示所有隐藏的标签页
          const showResult = await this.showWorkspaceUserTabs(workspace.id);
          if (!showResult.success) {
            return {
              success: false,
              error: showResult.error,
            };
          }

          console.log(`✅ 工作区用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'shown',
              tabIds: showResult.data!,
            },
          };

        case 'continue_hide':
          // 继续隐藏可见的标签页
          const continueHideResult = await this.continueHideWorkspaceUserTabs(workspace.id);
          if (!continueHideResult.success) {
            return {
              success: false,
              error: continueHideResult.error,
            };
          }

          console.log(`✅ 工作区用户标签页继续隐藏成功，影响 ${continueHideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'hidden',
              tabIds: continueHideResult.data!,
            },
          };

        case 'hide':
        default:
          // 隐藏所有可见的标签页
          const hideResult = await this.hideWorkspaceUserTabs(workspace.id);
          if (!hideResult.success) {
            return {
              success: false,
              error: hideResult.error,
            };
          }

          console.log(`✅ 工作区用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'hidden',
              tabIds: hideResult.data!,
            },
          };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle workspace user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 隐藏工作区的用户标签页
   */
  private static async hideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<number[]>> {
    // 实现隐藏逻辑...
    return { success: true, data: [] };
  }

  /**
   * 继续隐藏工作区的可见用户标签页
   */
  private static async continueHideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<number[]>> {
    // 实现继续隐藏逻辑...
    return { success: true, data: [] };
  }

  /**
   * 显示工作区的隐藏用户标签页
   */
  private static async showWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<number[]>> {
    // 实现显示逻辑...
    return { success: true, data: [] };
  }
}

// 类已在上面单独导出
