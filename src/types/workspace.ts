/**
 * 网站信息接口
 */
export interface Website {
  id: string;
  url: string;
  title: string;
  favicon: string;
  isPinned: boolean;
  addedAt: number;
  order: number; // 排序字段


}

/**
 * 工作区类型（Workona 风格）
 */
export type WorkspaceType = 'saved' | 'unsaved' | 'temp';

/**
 * 工作区状态（Workona 风格）
 */
export type WorkspaceState = 'active' | 'inactive';

/**
 * 工作区接口
 */
export interface WorkSpace {
  id: string;
  name: string;
  icon: string;
  color: string;
  websites: Website[];
  createdAt: number;
  updatedAt: number;
  isActive: boolean;
  order: number; // 排序字段
  windowId?: number; // 专用窗口ID（可选，用于专用窗口架构）
  userTabsHidden?: boolean; // 用户标签页是否已隐藏
  hiddenUserTabIds?: number[]; // 已隐藏的用户标签页ID列表

  // === Workona 风格扩展字段（可选，保持向后兼容） ===
  type?: WorkspaceType; // 工作区类型：saved/unsaved/temp
  pos?: number; // 位置标识符，用于 Workona 风格排序
  state?: WorkspaceState; // 工作区状态：active/inactive
  workonaTabIds?: string[]; // Workona 风格标签页ID列表 (t-{workspaceId}-{uuid})
  sessionId?: string; // 会话ID，用于会话隔离
  tabOrder?: string[]; // 标签页顺序（Workona ID）
}

/**
 * 用户设置接口
 */
export interface Settings {
  autoCloseOtherTabs: boolean;
  preserveUserOpenedTabs: boolean;
  defaultWorkspaceOnStartup: string;
  sidebarWidth: number;
  theme: 'dark' | 'light' | 'auto';
  showFavicons: boolean;
  confirmBeforeDelete: boolean;
  maxRecentWorkspaces: number;
}

/**
 * 存储数据结构
 */
export interface StorageData {
  workspaces: WorkSpace[];
  settings: Settings;
  activeWorkspaceId: string | null;
  lastActiveWorkspaceIds: string[]; // 最近使用的工作区ID列表

  // === Workona 风格扩展存储（可选，保持向后兼容） ===
  tabIdMappings?: TabIdMapping[]; // 标签页ID映射表
  localOpenWorkspaces?: LocalOpenWorkspaces; // 本地打开工作区
  tabGroups?: TabGroups; // 标签组信息
  workspaceSessions?: Record<string, WorkspaceSession>; // 工作区会话
  globalWorkspaceWindowId?: number; // 全局工作区窗口ID
  dataVersion?: string; // 数据版本，用于迁移管理
}

/**
 * 标签页信息接口
 */
export interface TabInfo {
  id: number;
  url: string;
  title: string;
  favicon: string;
  isPinned: boolean;
  isActive: boolean;
  windowId: number;

}

/**
 * 工作区切换选项
 */
export interface WorkspaceSwitchOptions {
  closeOtherTabs?: boolean;
  preserveUserOpenedTabs?: boolean;
  focusFirstTab?: boolean; // 默认为 false，不自动聚焦到第一个标签页
}

/**
 * 网站添加选项
 */
export interface AddWebsiteOptions {
  title?: string;
  favicon?: string;
  pinTab?: boolean;
  openInNewTab?: boolean;
}

/**
 * 工作区创建选项
 */
export interface CreateWorkspaceOptions {
  name: string;
  icon?: string;
  color?: string;
  websites?: Omit<Website, 'id' | 'addedAt' | 'order'>[];
  activate?: boolean;
}

/**
 * 工作区更新选项
 */
export interface UpdateWorkspaceOptions {
  name?: string;
  icon?: string;
  color?: string;
  websites?: Website[];
  isActive?: boolean;
}

/**
 * 错误类型
 */
export interface WorkspaceError {
  code: string;
  message: string;
  details?: any;
}

/**
 * 操作结果接口
 */
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: WorkspaceError;
}

/**
 * 事件类型
 */
export type WorkspaceEventType = 
  | 'workspace-created'
  | 'workspace-updated'
  | 'workspace-deleted'
  | 'workspace-switched'
  | 'website-added'
  | 'website-removed'
  | 'website-updated'
  | 'settings-updated';

/**
 * 事件数据接口
 */
export interface WorkspaceEvent {
  type: WorkspaceEventType;
  timestamp: number;
  data: any;
}

/**
 * 预设工作区模板
 */
export interface WorkspaceTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  websites: Omit<Website, 'id' | 'addedAt' | 'order'>[];
  category: 'development' | 'design' | 'research' | 'productivity' | 'ai-tools' | 'custom';
}

/**
 * 导入导出数据格式
 */
export interface ExportData {
  version: string;
  exportedAt: number;
  workspaces: WorkSpace[];
  settings: Partial<Settings>;
}

// ===== Workona 风格数据结构 =====

/**
 * Workona 风格标签页接口
 * 实现 t-{workspaceId}-{uuid} 命名规则
 */
export interface WorkspaceTab {
  id: string; // Workona 格式：t-{workspaceId}-{uuid}
  chromeTabId: number; // Chrome 原生标签页ID
  workspaceId: string; // 所属工作区ID
  url: string;
  title: string;
  favicon: string;
  isActive: boolean;
  isPinned: boolean;
  windowId: number;
  index: number; // 标签页在窗口中的位置
  discarded?: boolean; // 是否已卸载（内存优化）
  createdAt: number; // 创建时间
  lastActiveAt: number; // 最后激活时间
}

/**
 * 标签页ID映射接口
 * 用于 Workona ID 与 Chrome ID 的双向映射
 */
export interface TabIdMapping {
  workonaId: string; // t-{workspaceId}-{uuid} 格式
  chromeId: number; // Chrome 原生ID
  workspaceId: string; // 所属工作区ID
  websiteId?: string; // 对应的网站配置ID（如果有）
  createdAt: number; // 映射创建时间
  lastSyncAt: number; // 最后同步时间
}

/**
 * 工作区会话接口
 * 实现会话隔离机制
 */
export interface WorkspaceSession {
  workspaceId: string; // 工作区ID
  tabs: Record<string, WorkspaceTab>; // 标签页映射表（key: workonaId）
  tabOrder: string[]; // 标签页顺序（Workona ID 数组）
  activeTabId?: string; // 当前激活的标签页ID（Workona ID）
  lastActiveAt: number; // 会话最后活跃时间
  windowId?: number; // 关联的窗口ID
}

/**
 * 本地打开工作区接口
 * 实现 Workona 风格的本地工作区管理
 */
export interface LocalOpenWorkspace {
  id: string; // 工作区ID
  type: WorkspaceType; // 工作区类型
  tabs: WorkspaceTab[]; // 标签页列表
  lastActive: number; // 最后活跃时间
  tabCount: number; // 标签页数量
  isVisible: boolean; // 是否在主窗口可见
}

/**
 * 本地打开工作区集合
 */
export interface LocalOpenWorkspaces {
  [workspaceId: string]: LocalOpenWorkspace;
}

/**
 * 标签组详细信息接口
 * 对应 Workona 的 tabGroups 数据结构
 */
export interface TabGroupInfo {
  workspaceId: string; // 工作区ID
  all: WorkspaceTab[]; // 所有标签页
  visible: WorkspaceTab[]; // 可见标签页
  hidden: WorkspaceTab[]; // 隐藏标签页
  lastUpdated: number; // 最后更新时间
}

/**
 * 标签组集合
 */
export interface TabGroups {
  [workspaceId: string]: TabGroupInfo;
}

// ===== Workona 风格操作接口 =====

/**
 * Workona 标签页创建选项
 */
export interface WorkonaTabCreateOptions {
  workspaceId: string;
  url: string;
  title?: string;
  favicon?: string;
  isPinned?: boolean;
  isActive?: boolean;
  websiteId?: string; // 关联的网站配置ID
}

/**
 * 工作区会话切换选项
 */
export interface SessionSwitchOptions {
  preserveCurrentSession?: boolean; // 是否保存当前会话
  restoreTabOrder?: boolean; // 是否恢复标签页顺序
  activateFirstTab?: boolean; // 是否激活第一个标签页
  closeOtherWorkspaceTabs?: boolean; // 是否关闭其他工作区标签页
}

/**
 * Workona 数据迁移选项
 */
export interface MigrationOptions {
  backupOriginalData?: boolean; // 是否备份原始数据
  validateAfterMigration?: boolean; // 迁移后是否验证数据
  rollbackOnError?: boolean; // 出错时是否回滚
  preserveUserPreferences?: boolean; // 是否保留用户偏好设置
}

/**
 * Workona 标签页匹配结果
 */
export interface WorkonaTabMatchResult {
  isMatch: boolean;
  workspaceId?: string;
  websiteId?: string;
  workonaTabId?: string; // Workona 格式的标签页ID
  confidence: number; // 匹配置信度 (0-1)
  matchType: 'exact' | 'domain' | 'prefix' | 'none'; // 匹配类型
}
