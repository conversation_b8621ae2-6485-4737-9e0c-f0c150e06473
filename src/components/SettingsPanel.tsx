import React, { useState } from 'react';
import { X, Download, Upload, Trash2, CheckCircle, AlertCircle } from 'lucide-react';
import { StorageManager } from '@/utils/storage';

interface SettingsPanelProps {
  onClose: () => void;
}

/**
 * 设置面板组件 - 数据管理（增强版）
 */
const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const [operationStatus, setOperationStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });

  /**
   * 显示操作状态
   */
  const showStatus = (type: 'success' | 'error', message: string) => {
    setOperationStatus({ type, message });
    setTimeout(() => {
      setOperationStatus({ type: null, message: '' });
    }, 3000);
  };

  /**
   * 导出数据（增强版：包含完整数据结构）
   */
  const handleExport = async () => {
    try {
      setOperationStatus({ type: null, message: '正在导出数据...' });

      const result = await StorageManager.exportData();
      if (result.success) {
        const blob = new Blob([result.data!], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workspace-pro-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 解析导出数据以显示摘要
        const exportData = JSON.parse(result.data!);
        const summary = exportData.exportMetadata
          ? `${exportData.exportMetadata.totalWorkspaces} 个工作区, ${exportData.exportMetadata.totalWebsites} 个网站`
          : `${exportData.workspaces?.length || 0} 个工作区`;

        showStatus('success', `导出成功！包含 ${summary}`);
      } else {
        showStatus('error', '导出失败，请重试');
      }
    } catch (error) {
      console.error('Failed to export data:', error);
      showStatus('error', '导出过程中发生错误');
    }
  };

  /**
   * 导入数据（增强版：支持完整数据结构和验证）
   */
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          setOperationStatus({ type: null, message: '正在导入数据...' });

          const text = await file.text();

          // 预验证数据格式
          try {
            const importData = JSON.parse(text);
            if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
              showStatus('error', '无效的数据格式：缺少工作区数据');
              return;
            }

            // 显示导入预览
            const workspaceCount = importData.workspaces.length;
            const websiteCount = importData.workspaces.reduce((sum: number, ws: any) => sum + (ws.websites?.length || 0), 0);
            const version = importData.version || '1.0.0';

            const confirmMessage = `确定要导入以下数据吗？\n\n` +
              `数据版本: ${version}\n` +
              `工作区数量: ${workspaceCount}\n` +
              `网站数量: ${websiteCount}\n\n` +
              `注意：这将覆盖当前所有数据！`;

            if (!confirm(confirmMessage)) {
              setOperationStatus({ type: null, message: '' });
              return;
            }
          } catch (parseError) {
            showStatus('error', '无效的JSON文件格式');
            return;
          }

          const result = await StorageManager.importData(text);
          if (result.success) {
            showStatus('success', '导入成功！页面即将刷新...');
            setTimeout(() => {
              window.location.reload();
            }, 1500);
          } else {
            showStatus('error', '导入失败：' + (result.error?.message || '未知错误'));
          }
        } catch (error) {
          console.error('Failed to import data:', error);
          showStatus('error', '导入过程中发生错误');
        }
      }
    };
    input.click();
  };

  /**
   * 清除所有数据（增强版：包含确认和反馈）
   */
  const handleClearAll = async () => {
    const confirmMessage = '⚠️ 危险操作确认\n\n' +
      '这将永久删除以下所有数据：\n' +
      '• 所有工作区和网站\n' +
      '• 工作区会话和标签页状态\n' +
      '• 标签页ID映射\n' +
      '• 用户设置\n\n' +
      '此操作无法撤销！\n\n' +
      '确定要继续吗？';

    if (confirm(confirmMessage)) {
      try {
        setOperationStatus({ type: null, message: '正在清除所有数据...' });

        const result = await StorageManager.clearAll();
        if (result.success) {
          showStatus('success', '所有数据已清除！页面即将刷新...');
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          showStatus('error', '清除失败：' + (result.error?.message || '未知错误'));
        }
      } catch (error) {
        console.error('Failed to clear data:', error);
        showStatus('error', '清除过程中发生错误');
      }
    }
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in max-w-md">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            数据管理
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 操作状态显示 */}
        {operationStatus.message && (
          <div className={`p-3 rounded-lg mb-4 flex items-center gap-2 ${
            operationStatus.type === 'success'
              ? 'bg-green-900/50 border border-green-700 text-green-300'
              : operationStatus.type === 'error'
              ? 'bg-red-900/50 border border-red-700 text-red-300'
              : 'bg-blue-900/50 border border-blue-700 text-blue-300'
          }`}>
            {operationStatus.type === 'success' && <CheckCircle className="w-4 h-4" />}
            {operationStatus.type === 'error' && <AlertCircle className="w-4 h-4" />}
            <span className="text-sm">{operationStatus.message}</span>
          </div>
        )}

        {/* 数据管理内容 */}
        <div className="space-y-4">
          <button
            onClick={handleExport}
            className="w-full btn-secondary justify-center"
            disabled={operationStatus.message !== ''}
          >
            <Download className="w-4 h-4" />
            导出数据
          </button>
          <button
            onClick={handleImport}
            className="w-full btn-secondary justify-center"
            disabled={operationStatus.message !== ''}
          >
            <Upload className="w-4 h-4" />
            导入数据
          </button>
          <button
            onClick={handleClearAll}
            className="w-full btn-danger justify-center"
            disabled={operationStatus.message !== ''}
          >
            <Trash2 className="w-4 h-4" />
            清除所有数据
          </button>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end mt-6 pt-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;
