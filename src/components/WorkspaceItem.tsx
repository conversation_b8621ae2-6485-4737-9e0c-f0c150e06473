import React, { useState, useEffect } from 'react';
import {
  ChevronDown,
  ChevronRight,
  MoreVertical,
  Edit,
  Trash2,
  Plus,
  Link,
  Monitor,
  CheckSquare,
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react';
import { WorkSpace, Website } from '@/types/workspace';
import WebsiteList from './WebsiteList';
import DropdownMenu from './DropdownMenu';
import EditWorkspaceModal from './EditWorkspaceModal';
import AddWebsiteModal from './AddWebsiteModal';
import EditWebsiteModal from './EditWebsiteModal';
import ConfirmDialog from './ConfirmDialog';

interface WorkspaceItemProps {
  workspace: WorkSpace;
  isActive: boolean;
  isExpanded: boolean;
  onWorkspaceClick: () => void;
  onToggleExpand: () => void;
  onUpdateWorkspace: (updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: () => void;
  onAddCurrentTab: () => void;
  onAddWebsiteUrl: (url: string) => void;
  onRemoveWebsite: (websiteId: string) => void;
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWebsites: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
}

// 用户标签页状态接口
interface UserTabsState {
  isHidden: boolean;
  hiddenTabsCount: number;
  totalUserTabs: number;
  visibleUserTabs: number;
  canContinueHiding: boolean;
  actionType: 'hide' | 'continue_hide' | 'show';
  loading: boolean;
}

/**
 * 工作区项目组件
 */
const WorkspaceItem: React.FC<WorkspaceItemProps> = ({
  workspace,
  isActive,
  isExpanded,
  onWorkspaceClick,
  onToggleExpand,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab,
  onAddWebsiteUrl,
  onRemoveWebsite,
  onUpdateWebsite,
  onReorderWebsites,
  // Workona 风格：移除固定标签页相关参数
  onBatchDelete,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditWebsiteModal, setShowEditWebsiteModal] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [batchMode, setBatchMode] = useState(false);

  // 用户标签页状态
  const [userTabsState, setUserTabsState] = useState<UserTabsState>({
    isHidden: false,
    hiddenTabsCount: 0,
    totalUserTabs: 0,
    visibleUserTabs: 0,
    canContinueHiding: false,
    actionType: 'hide',
    loading: false,
  });
  /**
   * 加载工作区用户标签页状态
   */
  const loadUserTabsState = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 动态导入工作区用户标签页管理器
      const { WorkspaceUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);

      if (result.success) {
        const data = result.data!;
        setUserTabsState({
          isHidden: data.isHidden,
          hiddenTabsCount: data.hiddenTabIds.length,
          totalUserTabs: data.totalUserTabs,
          visibleUserTabs: data.visibleUserTabs,
          canContinueHiding: data.canContinueHiding,
          actionType: data.actionType,
          loading: false,
        });
      } else {
        console.error('获取工作区用户标签页状态失败:', result.error);
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('加载工作区用户标签页状态时出错:', error);
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 切换工作区用户标签页显示状态
   */
  const toggleUserTabsVisibility = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      const { WorkspaceUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await WorkspaceUserTabsVisibilityManager.toggleWorkspaceUserTabsVisibility(workspace);

      if (result.success) {
        // 重新加载状态
        await loadUserTabsState();
      } else {
        console.error('切换工作区用户标签页状态失败:', result.error);
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('切换工作区用户标签页状态时出错:', error);
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  // 当工作区变为活跃时加载用户标签页状态
  useEffect(() => {
    if (isActive) {
      loadUserTabsState();
    }
  }, [isActive, workspace.id]);








  /**
   * 处理菜单项点击
   */
  const handleMenuClick = (action: string) => {
    setShowDropdown(false);

    switch (action) {
      case 'edit':
        setShowEditModal(true);
        break;
      case 'delete':
        setShowDeleteConfirm(true);
        break;
      case 'add-url':
        setShowAddModal(true);
        break;
      case 'batch-operations':
        setBatchMode(!batchMode);
        // 进入批量模式时自动展开工作区
        if (!batchMode && !isExpanded) {
          onToggleExpand();
        }
        break;
    }
  };

  /**
   * 处理编辑工作区
   */
  const handleEditWorkspace = (updates: { name?: string; icon?: string; color?: string }) => {
    onUpdateWorkspace(updates);
    setShowEditModal(false);
  };

  /**
   * 处理删除确认
   */
  const handleDeleteConfirm = () => {
    onDeleteWorkspace();
    setShowDeleteConfirm(false);
  };

  /**
   * 处理添加网站URL
   */
  const handleAddWebsite = (url: string) => {
    onAddWebsiteUrl(url);
    setShowAddModal(false);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (website: Website) => {
    setEditingWebsite(website);
    setShowEditWebsiteModal(true);
  };

  /**
   * 处理保存网站编辑
   */
  const handleSaveWebsiteEdit = (updates: { url?: string; title?: string; isPinned?: boolean }) => {
    if (editingWebsite) {
      onUpdateWebsite(editingWebsite.id, updates);
      setShowEditWebsiteModal(false);
      setEditingWebsite(null);
    }
  };

  const menuItems = [
    {
      id: 'add-url',
      label: '添加网站URL',
      icon: Link,
    },
    ...(workspace.websites.length > 0 ? [{
      id: 'batch-operations',
      label: '批量操作',
      icon: CheckSquare,
    }] : []),
    {
      id: 'edit',
      label: '编辑工作区',
      icon: Edit,
    },
    {
      id: 'delete',
      label: '删除工作区',
      icon: Trash2,
      className: 'text-red-400 hover:text-red-300',
    },
  ];

  return (
    <div className={`workspace-item ${isActive ? 'active' : ''}`}>
      {/* 工作区头部 - 左对齐充满布局 */}
      <div className="flex items-center w-full">
        <div
          className="flex items-center gap-2 flex-1 min-w-0 cursor-pointer"
          onClick={onWorkspaceClick}
        >
          {/* 展开/折叠按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand();
            }}
            className="p-1 hover:bg-slate-600 rounded transition-colors duration-150 flex-shrink-0"
          >
            {isExpanded ? (
              <ChevronDown className="w-3.5 h-3.5 text-slate-400" />
            ) : (
              <ChevronRight className="w-3.5 h-3.5 text-slate-400" />
            )}
          </button>

          {/* 工作区图标 */}
          <div
            className="w-7 h-7 rounded flex items-center justify-center text-base flex-shrink-0"
            style={{ backgroundColor: workspace.color + '20', color: workspace.color }}
          >
            {workspace.icon}
          </div>

          {/* 工作区信息 - 充满剩余空间 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-white truncate text-sm">
                {workspace.name}
              </h3>
              {isActive && (
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse flex-shrink-0" />
              )}
              {/* Workona 风格：显示工作区类型 */}
              {workspace.type && workspace.type !== 'saved' && (
                <span className={`text-xs px-1.5 py-0.5 rounded text-white flex-shrink-0 ${
                  workspace.type === 'temp' ? 'bg-orange-600' : 'bg-blue-600'
                }`}>
                  {workspace.type === 'temp' ? '临时' : '未保存'}
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-slate-400">
              <span>{workspace.websites.length} 个网站</span>
            </div>
          </div>
        </div>

        {/* 操作按钮组 */}
        <div className="flex items-center gap-1">


          {/* 更多操作按钮 */}
          {/* 添加当前标签页按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onAddCurrentTab();
            }}
            className="p-2 hover:bg-slate-600 rounded transition-colors duration-150"
            title="添加当前标签页"
          >
            <Monitor className="w-4 h-4 text-slate-400 hover:text-green-400" />
          </button>

          {/* 用户标签页隐藏/显示按钮 */}
          {isActive && (
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleUserTabsVisibility();
                }}
                disabled={userTabsState.loading || userTabsState.totalUserTabs === 0}
                className={`p-2 rounded transition-colors duration-150 ${
                  userTabsState.loading || userTabsState.totalUserTabs === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:bg-slate-600'
                }`}
              title={
                userTabsState.totalUserTabs === 0
                  ? '没有用户标签页'
                  : userTabsState.actionType === 'show'
                  ? `显示 ${userTabsState.hiddenTabsCount} 个隐藏的用户标签页`
                  : userTabsState.actionType === 'continue_hide'
                  ? `继续隐藏 ${userTabsState.visibleUserTabs} 个可见的用户标签页`
                  : `隐藏 ${userTabsState.visibleUserTabs} 个用户标签页`
              }
            >
              {userTabsState.loading ? (
                <Loader2 className="w-4 h-4 text-slate-400 animate-spin" />
              ) : userTabsState.actionType === 'show' ? (
                <Eye className="w-4 h-4 text-slate-400 hover:text-blue-400" />
              ) : (
                <EyeOff className="w-4 h-4 text-slate-400 hover:text-orange-400" />
              )}
                {/* 显示用户标签页计数 */}
                {!userTabsState.loading && userTabsState.totalUserTabs > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {userTabsState.totalUserTabs}
                  </span>
                )}
              </button>
            </div>
          )}

          {/* 更多菜单按钮 */}
          <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowDropdown(!showDropdown);
            }}
            className="p-2 hover:bg-slate-600 rounded transition-colors duration-150"
          >
            <MoreVertical className="w-4 h-4 text-slate-400" />
          </button>

          {/* 下拉菜单 */}
          {showDropdown && (
            <DropdownMenu
              items={menuItems}
              onItemClick={handleMenuClick}
              onClose={() => setShowDropdown(false)}
            />
          )}
          </div>
        </div>
      </div>

      {/* 网站列表 - 左对齐充满 */}
      {isExpanded && workspace.websites.length > 0 && (
        <div className="mt-2">
          <WebsiteList
            websites={workspace.websites}
            onRemoveWebsite={onRemoveWebsite}
            onEditWebsite={handleEditWebsite}
            onReorderWebsites={onReorderWebsites}
            // Workona 风格：移除固定标签页相关属性
            onBatchDelete={onBatchDelete}
            batchMode={batchMode}
            onExitBatchMode={() => setBatchMode(false)}
          />
        </div>
      )}

      {/* 空状态 - 充满空间 */}
      {isExpanded && workspace.websites.length === 0 && (
        <div className="mt-2 p-3 border-2 border-dashed border-slate-600 rounded text-center">
          <Plus className="w-5 h-5 text-slate-500 mx-auto mb-2" />
          <p className="text-xs text-slate-400 mb-2">
            还没有添加任何网站
          </p>
          <div className="flex gap-1 justify-center">
            <button
              onClick={onAddCurrentTab}
              className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200"
              title="添加当前标签页"
            >
              <Monitor className="w-3.5 h-3.5 text-slate-400" />
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200"
              title="添加URL"
            >
              <Link className="w-3.5 h-3.5 text-slate-400" />
            </button>
          </div>
        </div>
      )}

      {/* 编辑工作区模态框 */}
      {showEditModal && (
        <EditWorkspaceModal
          workspace={workspace}
          onClose={() => setShowEditModal(false)}
          onSave={handleEditWorkspace}
        />
      )}

      {/* 添加网站模态框 */}
      {showAddModal && (
        <AddWebsiteModal
          onClose={() => setShowAddModal(false)}
          onAdd={handleAddWebsite}
        />
      )}

      {/* 编辑网站模态框 */}
      {showEditWebsiteModal && editingWebsite && (
        <EditWebsiteModal
          website={editingWebsite}
          onClose={() => {
            setShowEditWebsiteModal(false);
            setEditingWebsite(null);
          }}
          onSave={handleSaveWebsiteEdit}
        />
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <ConfirmDialog
          title="删除工作区"
          message={`确定要删除工作区"${workspace.name}"吗？此操作无法撤销。`}
          confirmText="删除"
          confirmButtonClass="btn-danger"
          onConfirm={handleDeleteConfirm}
          onCancel={() => setShowDeleteConfirm(false)}
        />
      )}
    </div>
  );
};

export default WorkspaceItem;
