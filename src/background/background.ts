// Service Worker 环境 polyfill
if (typeof window === 'undefined') {
  // 创建一个简单的 window 对象 polyfill
  (globalThis as any).window = {
    dispatchEvent: (event: Event) => {
      // 在 Service Worker 中，我们可以简单地忽略这些事件
      console.log('Service Worker: 忽略 window.dispatchEvent 调用:', event.type);
      return true;
    }
  };
}

import { StorageManager } from '../utils/storage';
import { WorkspaceSwitcher } from '../utils/workspaceSwitcher';
import { MigrationManager } from '../utils/dataMigration';
import { TabManager } from '../utils/tabs';
import { WorkonaTabManager } from '../utils/workonaTabManager';
import { WorkspaceManager } from '../utils/workspace';
import { COMMANDS } from '../utils/constants';

/**
 * Chrome扩展后台脚本
 */
class BackgroundService {
  constructor() {
    this.init();
  }

  /**
   * 初始化后台服务
   */
  private async init(): Promise<void> {
    // 设置侧边栏行为
    await this.setupSidePanel();

    // 监听命令
    this.setupCommandListeners();

    // 监听标签页事件
    this.setupTabListeners();

    // 监听存储变化
    this.setupStorageListeners();

    // 检查并执行数据迁移（新增）
    await this.checkAndMigrateData();

    // 清除活跃工作区状态，让用户手动选择
    await this.clearActiveWorkspaceOnStartup();

    // 初始化默认数据
    await this.initializeDefaultData();

    console.log('WorkSpace Pro background service initialized');
  }

  /**
   * 设置侧边栏
   */
  private async setupSidePanel(): Promise<void> {
    try {
      // 设置侧边栏在点击扩展图标时打开
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error('Failed to setup side panel:', error);
    }
  }

  /**
   * 设置命令监听器
   */
  private setupCommandListeners(): void {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log('Command received:', command);
      
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          case 'test_auto_classify':
            // 测试自动分类功能
            await this.testAutoClassify();
            break;
          default:
            console.log('Unknown command:', command);
        }
      } catch (error) {
        console.error('Error handling command:', command, error);
      }
    });
  }

  /**
   * 测试自动分类功能
   */
  private async testAutoClassify(): Promise<void> {
    try {
      console.log('🧪 [测试] 开始测试自动分类功能...');

      // 获取当前活跃标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        console.log('❌ [测试] 没有找到活跃标签页');
        return;
      }

      const activeTab = tabs[0];
      console.log('🧪 [测试] 当前活跃标签页:', {
        id: activeTab.id,
        url: activeTab.url,
        title: activeTab.title
      });

      if (!activeTab.id || !activeTab.url) {
        console.log('❌ [测试] 活跃标签页缺少ID或URL');
        return;
      }

      // 手动触发自动分类
      console.log('🧪 [测试] 手动触发自动分类...');
      const result = await TabManager.autoClassifyNewTab(activeTab.id, activeTab.url);
      console.log('🧪 [测试] 自动分类结果:', result);

      // 验证结果
      const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      console.log('🧪 [测试] 验证 Workona ID:', verifyResult);

    } catch (error) {
      console.error('❌ [测试] 测试自动分类功能失败:', error);
    }
  }

  /**
   * 设置标签页监听器
   */
  private setupTabListeners(): void {
    // 监听标签页激活（记录活跃标签页状态）
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        // 不再自动切换工作区，但记录标签页状态变化
        console.log('标签页激活，记录状态变化');

        // 获取标签页的Workona ID
        const { WorkspaceSessionManager } = await import('../utils/workspaceSessionManager');
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeInfo.tabId);

        if (workonaIdResult.success && workonaIdResult.data) {
          // 设置为当前活跃标签页
          await WorkspaceSessionManager.setActiveTab(workonaIdResult.data);
        }

        // 同步当前工作区状态
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      } catch (error) {
        console.error('Error handling tab activation:', error);
      }
    });

    // 监听标签页移动（记录标签页顺序变化）
    chrome.tabs.onMoved.addListener(async (_tabId, _moveInfo) => {
      try {
        console.log('标签页位置变化，同步工作区状态');

        // 延迟一点时间确保移动完成
        setTimeout(async () => {
          const { WorkspaceSessionManager } = await import('../utils/workspaceSessionManager');
          await WorkspaceSessionManager.syncCurrentWorkspaceState();
        }, 100);
      } catch (error) {
        console.error('Error handling tab move:', error);
      }
    });

    // 监听标签页创建（概念性重构：自动分类）
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log('🆕 [LISTENER] 标签页创建事件触发:', {
          id: tab.id,
          url: tab.url || '(no URL yet)',
          title: tab.title || '(no title yet)',
          status: tab.status,
          timestamp: new Date().toISOString()
        });

        // 注意：新创建的标签页可能还没有URL，主要依赖 onUpdated 监听器进行分类
        // 这里只处理有明确URL的情况
        if (tab.id && tab.url &&
            !tab.url.includes('chrome://') &&
            !tab.url.includes('chrome-extension://') &&
            !tab.url.includes('about:') &&
            tab.url !== 'chrome://newtab/') {
          console.log(`🎯 [LISTENER] 标签页创建时尝试自动分类: ID=${tab.id}, URL=${tab.url}`);
          try {
            console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
            const result = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
            console.log(`✨ [LISTENER] 已自动分类新标签页: ${tab.url}`);
          } catch (error) {
            console.error('❌ [LISTENER] 自动分类新标签页失败:', error);
          }
        } else {
          console.log(`⏭️ 跳过标签页创建时的自动分类: ID=${tab.id}, URL=${tab.url}, 原因: ${
            !tab.id ? '无ID' :
            !tab.url ? '无URL' :
            tab.url.includes('chrome://') ? '系统页面' :
            tab.url.includes('chrome-extension://') ? '扩展页面' :
            tab.url.includes('about:') ? 'about页面' :
            tab.url === 'chrome://newtab/' ? '新标签页' :
            '未知原因'
          }`);
        }

        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_created');
      } catch (error) {
        console.error('Error handling tab creation:', error);
      }
    });

    // 监听标签页更新（增强版：包含自动分类）
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      try {
        // 处理URL变化或加载完成的情况
        if ((changeInfo.url || changeInfo.status === 'complete') && tab.url) {
          console.log('🔄 [LISTENER] 标签页更新事件触发:', {
            id: tabId,
            url: tab.url,
            title: tab.title,
            status: tab.status,
            changeInfo: changeInfo,
            timestamp: new Date().toISOString()
          });

          // 概念性重构：自动分类更新的标签页
          // 检查是否需要分类（URL变化或首次加载完成）
          const shouldClassify = tab.url &&
              !tab.url.includes('chrome://') &&
              !tab.url.includes('chrome-extension://') &&
              !tab.url.includes('about:') &&
              tab.url !== 'chrome://newtab/' &&
              tab.url !== '';

          if (shouldClassify) {
            console.log(`🎯 [LISTENER] 标签页更新时尝试自动分类: ID=${tabId}, URL=${tab.url}`);
            try {
              console.log(`📞 [LISTENER] 调用 TabManager.autoClassifyNewTab...`);
              const result = await TabManager.autoClassifyNewTab(tabId, tab.url);
              console.log(`📞 [LISTENER] autoClassifyNewTab 返回结果:`, result);
              console.log(`✨ [LISTENER] 已自动分类更新的标签页: ${tab.url}`);
            } catch (error) {
              console.error('❌ [LISTENER] 自动分类更新标签页失败:', error);
            }
          } else {
            console.log(`⏭️ 跳过标签页更新时的自动分类: ID=${tabId}, URL=${tab.url}, 原因: ${
              !tab.url ? '无URL' :
              tab.url.includes('chrome://') ? '系统页面' :
              tab.url.includes('chrome-extension://') ? '扩展页面' :
              tab.url.includes('about:') ? 'about页面' :
              tab.url === 'chrome://newtab/' ? '新标签页' :
              tab.url === '' ? '空URL' :
              '未知原因'
            }`);
          }

          // 通知前端更新状态
          await this.notifyGlobalUserTabsStateChange('tab_updated');
        } else {
          console.log(`⏭️ 跳过标签页更新事件: ID=${tabId}, 原因: ${
            !changeInfo.url && changeInfo.status !== 'complete' ? '非URL变化且非完成状态' :
            !tab.url ? '无URL' :
            '未知原因'
          }`);
        }
      } catch (error) {
        console.error('Error handling tab update:', error);
      }
    });

    // 重复的监听器已移除，使用上面的增强版监听器

    // 监听标签页移除（增强版：清理 Workona ID 映射）
    chrome.tabs.onRemoved.addListener(async (tabId, _removeInfo) => {
      try {
        console.log('Tab removed:', tabId);

        // Workona 风格：清理标签页的 Workona ID 映射
        try {

          // 获取标签页的 Workona ID
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
          if (workonaIdResult.success && workonaIdResult.data) {
            const workonaId = workonaIdResult.data;
            const workspaceId = workonaId.split('-')[1]; // 从 t-{workspaceId}-{uuid} 提取工作区ID

            // 从工作区中移除 Workona 标签页ID
            await WorkspaceManager.removeWorkonaTabId(workspaceId, workonaId);

            // 移除 Workona ID 映射
            await WorkonaTabManager.removeTabMapping(workonaId);

            console.log(`🗑️ 清理已删除标签页的 Workona ID: ${workonaId}`);
          }
        } catch (workonaError) {
          console.error('清理 Workona ID 映射失败:', workonaError);
        }

        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_removed');
      } catch (error) {
        console.error('Error handling tab removal:', error);
      }
    });

    // 监听标签页移动
    chrome.tabs.onMoved.addListener(async (tabId, moveInfo) => {
      try {
        console.log('Tab moved:', tabId, moveInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_moved');
      } catch (error) {
        console.error('Error handling tab move:', error);
      }
    });

    // 监听标签页附加到窗口
    chrome.tabs.onAttached.addListener(async (tabId, attachInfo) => {
      try {
        console.log('Tab attached:', tabId, attachInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_attached');
      } catch (error) {
        console.error('Error handling tab attach:', error);
      }
    });

    // 监听标签页从窗口分离
    chrome.tabs.onDetached.addListener(async (tabId, detachInfo) => {
      try {
        console.log('Tab detached:', tabId, detachInfo);
        // 通知前端更新全局用户标签页状态
        await this.notifyGlobalUserTabsStateChange('tab_detached');
      } catch (error) {
        console.error('Error handling tab detach:', error);
      }
    });
  }

  /**
   * 设置存储监听器
   */
  private setupStorageListeners(): void {
    StorageManager.onChanged((changes) => {
      console.log('Storage changed:', changes);
      
      // 通知侧边栏更新
      this.notifySidePanelUpdate(changes);
    });
  }

  /**
   * 清除活跃工作区状态，确保浏览器重启后不会自动选择工作区
   */
  private async clearActiveWorkspaceOnStartup(): Promise<void> {
    try {
      console.log('🔄 清除活跃工作区状态，让用户手动选择');
      await StorageManager.setActiveWorkspaceId(null);

      // 同时清除所有工作区的 isActive 状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data) {
        const workspaces = workspacesResult.data.map(workspace => ({
          ...workspace,
          isActive: false
        }));
        await StorageManager.saveWorkspaces(workspaces);
      }

      console.log('✅ 活跃工作区状态已清除');
    } catch (error) {
      console.error('❌ 清除活跃工作区状态失败:', error);
    }
  }

  /**
   * 初始化默认数据
   */
  private async initializeDefaultData(): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data!.length === 0) {
        console.log('No workspaces found, creating default workspace templates');

        // 可以选择性地创建一些默认工作区
        // 这里暂时不自动创建，让用户自己选择
      }
    } catch (error) {
      console.error('Error initializing default data:', error);
    }
  }

  /**
   * 根据索引切换工作区
   */
  private async switchToWorkspaceByIndex(index: number): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('Failed to get workspaces:', workspacesResult.error);
        return;
      }

      const workspaces = workspacesResult.data!;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        const result = await WorkspaceSwitcher.switchToWorkspace(workspace.id);
        if (result.success) {
          console.log(`Switched to workspace: ${workspace.name}`);
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        } else {
          console.error('Failed to switch workspace:', result.error);
        }
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error('Error switching workspace by index:', error);
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  private async toggleSidePanel(): Promise<void> {
    try {
      // 获取当前活跃的标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id!;
        // 这里可以实现侧边栏的切换逻辑
        // 由于Chrome API限制，我们主要依赖用户点击扩展图标
        console.log('Toggle side panel for tab:', tabId);
      }
    } catch (error) {
      console.error('Error toggling side panel:', error);
    }
  }

  /**
   * 显示通知
   */
  private showNotification(message: string, icon?: string): void {
    try {
      // 创建简单的通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'WorkSpace Pro',
        message: `${icon || '🚀'} ${message}`,
      });
      console.log('Notification shown:', message);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  /**
   * 通知侧边栏更新
   */
  private notifySidePanelUpdate(_changes: { [key: string]: chrome.storage.StorageChange }): void {
    try {
      // 这里可以通过消息传递通知侧边栏更新
      // 由于侧边栏是独立的页面，我们主要依赖存储监听
      console.log('Notifying side panel of storage changes');
    } catch (error) {
      console.error('Error notifying side panel:', error);
    }
  }



  /**
   * 通知全局用户标签页状态变化
   */
  private async notifyGlobalUserTabsStateChange(eventType: string): Promise<void> {
    try {
      // 延迟一小段时间，确保标签页操作完成
      setTimeout(async () => {
        try {
          // 在background script中，只发送Chrome扩展消息，不使用window对象
          if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({
              type: 'USER_TABS_VISIBILITY_CHANGED',
              workspaceId: 'global',
              eventType: eventType
            }).catch(error => {
              console.log('发送用户标签页状态变化消息失败:', error);
            });
          }

          console.log(`📢 已通知全局用户标签页状态变化: ${eventType}`);
        } catch (error) {
          console.error('发送全局用户标签页状态变化通知失败:', error);
        }
      }, 50); // 延迟50ms确保操作完成
    } catch (error) {
      console.error('通知全局用户标签页状态变化失败:', error);
    }
  }

  /**
   * 检查并执行数据迁移
   */
  private async checkAndMigrateData(): Promise<void> {
    try {
      console.log('🔍 检查数据迁移需求...');

      // 检测当前数据版本
      const versionResult = await MigrationManager.detectDataVersion();
      if (!versionResult.success) {
        console.error('检测数据版本失败:', versionResult.error);
        return;
      }

      const currentVersion = versionResult.data!;
      console.log(`📊 当前数据版本: ${currentVersion}`);

      // 如果是旧版本数据，执行迁移
      if (currentVersion !== '1.0.0') {
        console.log('🚀 开始执行数据迁移...');

        const migrationResult = await MigrationManager.migrateToWorkonaFormat({
          backupOriginalData: true,
          validateAfterMigration: true,
          rollbackOnError: true,
          preserveUserPreferences: true
        });

        if (migrationResult.success) {
          if (migrationResult.data) {
            console.log('✅ 基础数据迁移成功完成');

            // 概念性重构：标签页元数据迁移
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data! > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          } else {
            console.log('ℹ️ 数据已是最新版本，无需迁移');

            // 即使无需基础迁移，也检查元数据迁移
            const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
            if (metadataMigrationResult.success && metadataMigrationResult.data! > 0) {
              console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
            }
          }
        } else {
          console.error('❌ 数据迁移失败:', migrationResult.error);
        }
      } else {
        console.log('✅ 数据版本已是最新，无需迁移');
      }
    } catch (error) {
      console.error('❌ 数据迁移检查过程中发生错误:', error);
    }
  }
}

// 初始化后台服务
new BackgroundService();
